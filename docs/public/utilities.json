{"array": [{"name": "chunk", "description": "Creates an array of elements split into groups the length of size", "category": "array", "dependencies": [], "tags": ["array", "chunk", "split"], "examples": ["chunk([1, 2, 3, 4, 5], 2) // [[1, 2], [3, 4], [5]]", "chunk(['a', 'b', 'c', 'd'], 3) // [['a', 'b', 'c'], ['d']]"], "filename": "chunk"}, {"name": "flatten", "description": "Flattens array a single level deep", "category": "array", "dependencies": [], "tags": ["array", "flatten", "nested"], "examples": ["flatten([1, [2, 3], 4]) // [1, 2, 3, 4]", "flatten(['a', ['b', 'c'], 'd']) // ['a', 'b', 'c', 'd']"], "filename": "flatten"}, {"name": "flattenDeep", "description": "Recursively flattens array to a single level", "category": "array", "dependencies": [], "tags": ["array", "flatten", "recursive", "deep"], "examples": ["flattenDeep([1, [2, [3, [4]]]]) // [1, 2, 3, 4]", "flattenDeep(['a', ['b', ['c', 'd']]]) // ['a', 'b', 'c', 'd']"], "filename": "flattenDeep"}, {"name": "sample", "description": "Gets a random element from array", "category": "array", "dependencies": [], "tags": ["array", "random", "sample"], "examples": ["sample([1, 2, 3, 4, 5]) // Random element", "sample(['a', 'b', 'c']) // Random element"], "filename": "sample"}, {"name": "sampleSize", "description": "Gets n random elements from array", "category": "array", "dependencies": [], "tags": ["array", "random", "sample", "multiple"], "examples": ["sampleSize([1, 2, 3, 4, 5], 3) // 3 random elements", "sampleSize(['a', 'b', 'c', 'd'], 2) // 2 random elements"], "filename": "sampleSize"}, {"name": "uniq", "description": "Creates a duplicate-free version of an array", "category": "array", "dependencies": [], "tags": ["array", "unique", "duplicates"], "examples": ["uniq([1, 2, 2, 3, 3, 4]) // [1, 2, 3, 4]", "uniq(['a', 'b', 'a', 'c']) // ['a', 'b', 'c']"], "filename": "uniq"}, {"name": "uniqBy", "description": "Creates a duplicate-free version of an array using iteratee function", "category": "array", "dependencies": [], "tags": ["array", "unique", "duplicates", "iteratee"], "examples": ["uniqBy([{id: 1}, {id: 2}, {id: 1}], item => item.id) // [{id: 1}, {id: 2}]", "uniqBy(['a', 'bb', 'ccc', 'dd'], item => item.length) // ['a', 'bb', 'ccc']"], "filename": "uniqBy"}], "string": [{"name": "camelCase", "description": "Converts string to camelCase format", "category": "string", "dependencies": [], "tags": ["string", "case", "conversion"], "examples": ["camelCase('hello world') // 'helloWorld'", "camelCase('Hello_World') // 'helloWorld'"], "filename": "camelCase"}, {"name": "capitalize", "description": "Capitalizes the first character of string", "category": "string", "dependencies": [], "tags": ["string", "capitalize", "text"], "examples": ["capitalize('hello') // 'Hello'", "capitalize('HELLO') // 'Hello'"], "filename": "capitalize"}, {"name": "debounce", "description": "Creates a debounced function that delays invoking func", "category": "string", "dependencies": [], "tags": ["function", "debounce", "timing", "performance"], "examples": ["const debouncedFn = debounce(fn, 300)", "const immediateFn = debounce(fn, 300, true)"], "filename": "debounce"}, {"name": "kebabCase", "description": "Converts string to kebab-case format", "category": "string", "dependencies": [], "tags": ["string", "case", "conversion"], "examples": ["kebabCase('helloWorld') // 'hello-world'", "kebabCase('Hello World') // 'hello-world'"], "filename": "kebabCase"}, {"name": "pascalCase", "description": "Converts string to PascalCase format", "category": "string", "dependencies": [], "tags": ["string", "case", "conversion"], "examples": ["pascalCase('hello world') // 'HelloWorld'", "pascalCase('hello-world') // 'HelloWorld'"], "filename": "pascalCase"}, {"name": "snakeCase", "description": "Converts string to snake_case format", "category": "string", "dependencies": [], "tags": ["string", "case", "conversion"], "examples": ["snakeCase('helloWorld') // 'hello_world'", "snakeCase('Hello World') // 'hello_world'"], "filename": "snakeCase"}, {"name": "startCase", "description": "Converts string to start case (Title Case)", "category": "string", "dependencies": [], "tags": ["string", "case", "conversion", "title"], "examples": ["startCase('hello_world') // 'Hello World'", "startCase('helloWorld') // 'Hello World'"], "filename": "startCase"}, {"name": "throttle", "description": "Creates a throttled function that only invokes func at most once per limit", "category": "string", "dependencies": [], "tags": ["function", "throttle", "timing", "performance"], "examples": ["const throttledFn = throttle(fn, 1000)", "throttledFn() // Called immediately"], "filename": "throttle"}, {"name": "truncate", "description": "Truncates string with customizable options", "category": "string", "dependencies": [], "tags": ["string", "truncate", "text"], "examples": ["truncate('hello world', { length: 8 }) // 'hello...'", "truncate('hello world', { length: 8, omission: '---' }) // 'hello---'"], "filename": "truncate"}], "number": [{"name": "ceil", "description": "Computes number rounded up to precision", "category": "number", "dependencies": [], "tags": ["number", "ceil", "precision"], "examples": ["ceil(1.2345, 2) // 1.24", "ceil(1.2345) // 2"], "filename": "ceil"}, {"name": "clamp", "description": "Clamps number within the inclusive lower and upper bounds", "category": "number", "dependencies": [], "tags": ["number", "clamp", "bounds"], "examples": ["clamp(5, 1, 10) // 5", "clamp(-5, 1, 10) // 1", "clamp(15, 1, 10) // 10"], "filename": "clamp"}, {"name": "floor", "description": "Computes number rounded down to precision", "category": "number", "dependencies": [], "tags": ["number", "floor", "precision"], "examples": ["floor(1.2345, 2) // 1.23", "floor(1.2345) // 1"], "filename": "floor"}, {"name": "inRange", "description": "Checks if number is between start and up to, but not including, end", "category": "number", "dependencies": [], "tags": ["number", "range", "check"], "examples": ["inRange(5, 1, 10) // true", "inRange(0, 1, 10) // false", "inRange(5, 10) // true (0 to 10)"], "filename": "inRange"}, {"name": "percentage", "description": "Calculates percentage with precision", "category": "number", "dependencies": [], "tags": ["number", "percentage", "calculation"], "examples": ["percentage(25, 100) // 25", "percentage(1, 3, 2) // 33.33"], "filename": "percentage"}, {"name": "random", "description": "Produces a random number between the inclusive lower and upper bounds", "category": "number", "dependencies": [], "tags": ["number", "random", "generator"], "examples": ["random(0, 10) // Random integer between 0-10", "random(0, 1, true) // Random float between 0-1"], "filename": "random"}, {"name": "randomInt", "description": "Produces a random integer between the inclusive min and max bounds", "category": "number", "dependencies": [], "tags": ["number", "random", "integer"], "examples": ["randomInt(1, 5) // Random integer between 1-5", "randomInt(10, 20) // Random integer between 10-20"], "filename": "randomInt"}, {"name": "round", "description": "Rounds number to precision", "category": "number", "dependencies": [], "tags": ["number", "round", "precision"], "examples": ["round(1.2345, 2) // 1.23", "round(1.2365, 2) // 1.24"], "filename": "round"}]}