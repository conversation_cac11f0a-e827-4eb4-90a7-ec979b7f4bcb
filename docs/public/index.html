<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عون (Awn) - TypeScript Utility Library</title>
    <meta name="description" content="A shadcn-style utility library for TypeScript. Copy utilities into your project.">
    <link rel="icon"
          href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">
    <link href="./styles.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
<div class="container">
    <header class="header">
        <div class="hero">
            <h1 class="title">
                <span class="arabic">عون</span>
                <span class="english">(Awn)</span>
            </h1>
            <p class="subtitle">A shadcn-style utility library for TypeScript</p>
            <p class="description">Copy utilities into your project. No runtime dependencies.</p>
            <div class="actions">
                <code class="install-cmd">npm install -g awn</code>
                <a href="https://github.com/MuhammedAlkhudiry/awn" class="github-link" target="_blank">
                    GitHub →
                </a>
            </div>
        </div>
    </header>

    <nav class="nav">
        <div class="nav-content">
            <a href="#installation">Installation</a>
            <a href="#configuration">Configuration</a>
            <a href="#array">Array Utils</a>
            <a href="#string">String Utils</a>
            <a href="#number">Number Utils</a>
        </div>
    </nav>

    <main class="main">
        <section id="installation" class="section">
            <h2>Installation</h2>
            <div class="code-block">
                    <pre><code class="language-bash"># Install globally
npm install -g awn

# Initialize configuration (optional)
npx awn init

# Add utilities to your project
npx awn add debounce</code></pre>
            </div>
        </section>

        <section id="configuration" class="section">
            <h2>Configuration</h2>
            <p>Create a <code>utils.json</code> file to customize paths and aliases:</p>
            <div class="code-block">
                    <pre><code class="language-json">{
  "$schema": "https://awn.dev/schema.json",
  "utils": {
    "path": "./src/utils"
  },
  "aliases": {
    "utils": "~/utils",
    "~": "./src",
    "@": "./src"
  }
}</code></pre>
            </div>
        </section>

        <section id="array" class="section">
            <h2>Array Utilities</h2>
            <div class="utilities-grid" id="array-utils">
                <!-- Array utilities will be populated by JavaScript -->
            </div>
        </section>

        <section id="string" class="section">
            <h2>String Utilities</h2>
            <div class="utilities-grid" id="string-utils">
                <!-- String utilities will be populated by JavaScript -->
            </div>
        </section>

        <section id="number" class="section">
            <h2>Number Utilities</h2>
            <div class="utilities-grid" id="number-utils">
                <!-- Number utilities will be populated by JavaScript -->
            </div>
        </section>
    </main>

    <footer class="footer">
        <p>Made with ❤️ by <a href="https://github.com/MuhammedAlkhudiry" target="_blank">Muhammed Alkhudiry</a></p>
        <p>MIT License • <a href="https://github.com/MuhammedAlkhudiry/awn" target="_blank">Source Code</a></p>
    </footer>
</div>

<script src="./script.js"></script>
</body>
</html>