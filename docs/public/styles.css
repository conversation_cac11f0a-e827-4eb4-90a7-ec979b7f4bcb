* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --color-bg: #fafafa;
    --color-fg: #171717;
    --color-primary: #0ea5e9;
    --color-secondary: #64748b;
    --color-border: #e2e8f0;
    --color-accent: #f1f5f9;
    --color-success: #22c55e;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

[data-theme="dark"] {
    --color-bg: #0a0a0a;
    --color-fg: #fafafa;
    --color-border: #262626;
    --color-accent: #171717;
    --color-secondary: #a1a1aa;
}

body {
    font-family: var(--font-sans);
    background: var(--color-bg);
    color: var(--color-fg);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    text-align: center;
    padding: 4rem 0;
    border-bottom: 1px solid var(--color-border);
}

.title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--color-primary), #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.arabic {
    font-size: 4rem;
    margin-right: 0.5rem;
}

.english {
    font-size: 3rem;
    font-weight: 600;
}

.subtitle {
    font-size: 1.5rem;
    color: var(--color-secondary);
    margin-bottom: 0.5rem;
}

.description {
    font-size: 1.1rem;
    color: var(--color-secondary);
    margin-bottom: 2rem;
}

.actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.install-cmd {
    background: var(--color-accent);
    border: 1px solid var(--color-border);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-family: var(--font-mono);
    font-size: 0.9rem;
}

.github-link {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: opacity 0.2s;
}

.github-link:hover {
    opacity: 0.8;
}

/* Navigation */
.nav {
    position: sticky;
    top: 0;
    background: var(--color-bg);
    border-bottom: 1px solid var(--color-border);
    z-index: 100;
    backdrop-filter: blur(8px);
}

.nav-content {
    display: flex;
    gap: 2rem;
    padding: 1rem 0;
    overflow-x: auto;
}

.nav a {
    color: var(--color-secondary);
    text-decoration: none;
    font-weight: 500;
    white-space: nowrap;
    padding: 0.5rem 0;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.nav a:hover,
.nav a.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    margin-bottom: 4rem;
}

.section h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--color-fg);
}

.section p {
    color: var(--color-secondary);
    margin-bottom: 1rem;
}

/* Code Blocks */
.code-block {
    background: var(--color-accent);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    overflow: hidden;
    margin: 1.5rem 0;
}

.code-block pre {
    padding: 1.5rem;
    overflow-x: auto;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
}

code {
    font-family: var(--font-mono);
    font-size: 0.9em;
    background: var(--color-accent);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    border: 1px solid var(--color-border);
}

/* Utility Cards */
.utilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.utility-card {
    background: var(--color-accent);
    border: 1px solid var(--color-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s;
}

.utility-card:hover {
    border-color: var(--color-primary);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.1);
}

.utility-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
}

.utility-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-fg);
    font-family: var(--font-mono);
}

.utility-add {
    background: var(--color-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    font-family: var(--font-mono);
    transition: all 0.2s;
    margin-left: auto;
}

.utility-add:hover {
    background: #0284c7;
}

.utility-description {
    color: var(--color-secondary);
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.utility-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.tag {
    background: var(--color-border);
    color: var(--color-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.utility-example {
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.utility-example pre {
    font-family: var(--font-mono);
    font-size: 0.85rem;
    color: var(--color-fg);
    overflow-x: auto;
}

/* Footer */
.footer {
    text-align: center;
    padding: 3rem 0;
    border-top: 1px solid var(--color-border);
    color: var(--color-secondary);
    margin-top: 4rem;
}

.footer a {
    color: var(--color-primary);
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* Responsive */
@media (max-width: 768px) {
    .title {
        font-size: 2.5rem;
    }

    .arabic {
        font-size: 3rem;
    }

    .english {
        font-size: 2rem;
    }

    .actions {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-content {
        gap: 1rem;
    }

    .utilities-grid {
        grid-template-columns: 1fr;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection */
::selection {
    background: var(--color-primary);
    color: white;
}