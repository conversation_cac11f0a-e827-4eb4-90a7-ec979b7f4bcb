<?php

use App\Models\Guest;
use App\Models\Member;
use App\Models\Node;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia;

it('returns welcome page with correct statistics', function () {
    // Arrange
    Node::factory(3)->create();
    Tenant::factory(2)->create();
    User::factory(2)->create();
    Member::factory(2)->create();
    Guest::factory(1)->create();

    // Act
    $response = $this->get('/');

    // Assert
    $response->assertStatus(200);
    $response->assertInertia(
        fn(AssertableInertia $page) => $page
            ->component('Landing/Welcome')
            ->where('nodesCount', 3)
            ->where('tenantsCount', 2)
            ->where('usersCount', 5)
    );
});

it('caches welcome page data', function () {
    // Arrange
    Cache::shouldReceive('flexible')
        ->once()
        ->withArgs(
            fn($key, $ttl, $callback) => $key === 'welcome-data' &&
                $ttl === [30, 60 * 5] &&
                is_callable($callback)
        )
        ->andReturn([
            'nodesCount' => 0,
            'tenantsCount' => 0,
            'usersCount' => 0,
        ]);

    // Act
    $response = $this->get('/');

    // Assert
    $response->assertStatus(200);
    $response->assertInertia(
        fn($page) => $page
            ->component('Landing/Welcome')
            ->has('nodesCount')
            ->has('tenantsCount')
            ->has('usersCount')
    );
});
