<?php

namespace App\Http\Requests;

use App\Enums\Gender;
use App\Enums\LifeStatus;
use App\Models\Branch;
use App\Models\Node;
use App\Models\Relationship;
use App\Services\FixMisspelledNodeNameService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use function tenant_exists;

class NodeFormRequest extends FormRequest
{
    public function rules(): array
    {
        return collect([
            'name' => ['required', 'string', 'max:255'],
            'photo' => ['nullable', 'image'],
            'order' => ['nullable', 'integer'],
            'nickname' => ['nullable', 'string', 'max:255'],
            'bg_color' => ['nullable', 'string', 'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/'],
            'label' => ['nullable', 'string', 'max:255'],
            'size' => ['nullable', 'numeric'],
            'mobile' => [
                'nullable',
                'numeric',
                Rule::unique('nodes', 'mobile')->when(
                    $this->node,
                    fn($rule) => $rule->ignoreModel($this->node)
                ),
            ],
            'email' => [
                'nullable',
                'email',
                Rule::unique('nodes', 'email')->when(
                    $this->node,
                    fn($rule) => $rule->ignoreModel($this->node)
                ),
            ],
            'gender' => ['required', Rule::enum(Gender::class)],
            'life_status' => ['required', Rule::enum(LifeStatus::class)],
            'country_id' => ['nullable', 'numeric', 'exists:countries,id'],
            'city_id' => ['nullable', 'numeric', 'exists:cities,id'],
            'district_id' => ['nullable', 'numeric', 'exists:districts,id'],
            'about' => ['nullable', 'string'],
            'relationships' => ['nullable', 'array'],
            'relationships.*.node_id' => ['nullable', tenant_exists('nodes', 'id')],
            'relationships.*.name' => ['nullable', 'string', 'max:255'],
            'relationships.*.family_name' => ['nullable', 'string', 'max:255'],
            'relationships.*.status' => [
                'required',
                Rule::in([Relationship::MARRIAGE, Relationship::DIVORCE, Relationship::WIDOW]),
            ],
            'other_parent_relationship_id' => ['nullable', 'exists:relationships,id'],
        ])
            ->when(
                $this->isMethod('POST') && !$this->node,
                fn($rules) => $rules->put('parent_id', ['required', tenant_exists('nodes', 'id')])
            )
            ->toArray();
    }

    public function prepareData(): array
    {
        /** @var ?Node $node */
        $node = $this->route('node');

        return [
            'name' => FixMisspelledNodeNameService::handle($this->name),
            'branch_id' => Branch::value('id'),
            'nickname' => $this->nickname ?? $node?->nickname,
            'bg_color' => $this->bg_color ?? $node?->bg_color,
            'size' => $this->size ?? $node?->size,
            'label' => $this->label ?? $node?->label,
            'mobile' => $this->mobile ?? $node?->mobile,
            'email' => $this->email ?? $node?->email,
            'gender' => $this->gender ?? $node?->gender,
            'parent_id' => $this->parent_id ?? $node?->parent_id,
            'life_status' => $this->life_status ?? $node?->life_status,
            'birth_date' =>
                ($this->birth_date ? hijri_to_gregorian($this->birth_date) : null) ??
                $node?->birth_date,
            'death_date' =>
                ($this->death_date && $this->life_status === LifeStatus::dead->value
                    ? hijri_to_gregorian($this->death_date)
                    : null) ?? $node?->death_date,
            'country_id' => $this->country_id ?? $node?->country_id,
            'city_id' => $this->city_id ?? $node?->city_id,
            'district_id' => $this->district_id ?? $node?->district_id,
            'order' => $this->order ?? $node?->order,
            'photo' => $this->file('photo')?->store('photos') ?? $node?->photo,
            'about' => $this->about ?? $node?->about,
            'relationships' => $this->relationships,
            'other_parent_relationship_id' => $this->other_parent_relationship_id,
        ];
    }
}
