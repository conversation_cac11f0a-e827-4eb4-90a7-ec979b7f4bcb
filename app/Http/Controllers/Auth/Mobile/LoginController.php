<?php

namespace App\Http\Controllers\Auth\Mobile;

use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use App\Models\OneTimePassword;
use App\Services\FirstOrCreateMemberOrUser;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class LoginController extends Controller
{
    public function identifier(Request $request)
    {
        $request->validate([
            'identifier' => ['required'],
            'identifier_type' => ['required', Rule::enum(IdentifierType::class)],
            'url' => ['nullable', 'string', 'max:255'],
        ]);

        $memberOrUser = (new FirstOrCreateMemberOrUser())->handle(
            $request->identifier,
            $request->identifier_type,
            $request->url
        );

        if (!$memberOrUser) {
            return invalidate(
                'رقم الجوال أو البريد الإلكتروني غير مسجل في شجرة العائلة، تواصل مع مسؤولي الشجرة لإضافة بياناتك للشجرة.'
            );
        }

        if (!$memberOrUser->tenant->mobileAppEnabled()) {
            return invalidate(
                'الشجرة غير مفعلة في التطبيق، تواصل مع مسؤولي الشجرة لتفعيل التطبيق من خلال إعدادات الموقع.'
            );
        }

        OneTimePassword::sendTo(
            $request->identifier,
            IdentifierType::from($request->identifier_type)
        );

        return ok();
    }

    public function otp(Request $request)
    {
        $request->validate([
            'identifier' => ['required'],
            'identifier_type' => ['required', Rule::enum(IdentifierType::class)],
            'otp' => ['required', 'numeric'],
            'device_name' => ['required', 'string', 'max:255'],
        ]);

        OneTimePassword::ensureOtpIsValid(
            $request->identifier,
            IdentifierType::from($request->identifier_type),
            $request->otp
        );

        $memberOrUser = (new FirstOrCreateMemberOrUser())->handle(
            $request->identifier,
            $request->identifier_type
        );

        return json([
            'token' => $memberOrUser->createToken($request->device_name)->plainTextToken,
        ]);
    }

    public function logout()
    {
        auth()->user()->currentAccessToken()->delete();

        return ok();
    }
}
