<?php

namespace App\Http\Controllers\Auth\User\Register;

use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterInfoRequest;
use App\Models\OneTimePassword;
use App\Services\CreateTenant;
use App\Services\LoginService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;

class RegisterInfoController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            function ($request, $next) {
                if (session('oauth')) {
                    return $next($request);
                }

                $type = IdentifierType::tryFrom($request->identifier_type);

                if (
                    !$request->identifier ||
                    !$type ||
                    !OneTimePassword::current($request->identifier, $type)
                ) {
                    return redirect()->route('login.create');
                }

                return $next($request);
            },
        ];
    }

    public function create(Request $request)
    {
        return inertia('Tenant/Auth/Register/Info', [
            'oauth' => session('oauth'),
            'identifier' => $request->identifier,
            'identifier_type' => $request->identifier_type,
        ]);
    }

    public function store(RegisterInfoRequest $request)
    {
        $tenant = CreateTenant::handle([
            'name' => $request->name,
            'identifier' => session('oauth.email') ?? $request->identifier,
            'identifier_type' => session('oauth.email')
                ? IdentifierType::Email->value
                : $request->identifier_type,
            'family_name' => $request->family_name,
            'root_name' => $request->root_name,
        ]);

        $loginType = session('oauth.type') ?? $request->identifier_type;

        LoginService::handle(user: $tenant->owner, type: $loginType);

        postHog(['event' => 'Register: ' . $loginType]);

        return redirect()
            ->route('onboarding')
            ->with(['success' => 'تم التسجيل بنجاح. مرحبًا بك!']);
    }
}
