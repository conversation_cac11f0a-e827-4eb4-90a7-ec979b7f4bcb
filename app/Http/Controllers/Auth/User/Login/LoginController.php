<?php

namespace App\Http\Controllers\Auth\User\Login;

use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use App\Models\OneTimePassword;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class LoginController extends Controller
{
    public function create()
    {
        return inertia('Tenant/Auth/Login/Login');
    }

    public function store(Request $request)
    {
        $request->validate([
            'identifier' => 'required',
            'identifier_type' => ['required', Rule::enum(IdentifierType::class)],
        ]);

        OneTimePassword::sendTo(
            $request->identifier,
            IdentifierType::from($request->identifier_type)
        );

        $user = User::byIdentifier(
            $request->identifier,
            IdentifierType::from($request->identifier_type)
        );

        session([
            'after_otp_url' => $user
                ? route('login.login-after-otp')
                : route('register.info.create'),
        ]);

        return redirect()->route('otp.create', [
            'identifier' => $request->identifier,
            'identifier_type' => $request->identifier_type,
        ]);
    }
}
