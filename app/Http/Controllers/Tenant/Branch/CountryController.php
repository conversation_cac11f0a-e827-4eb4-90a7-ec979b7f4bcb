<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Country;
use Illuminate\Http\Request;

class CountryController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'text' => ['required', 'string', 'max:255'],
        ]);

        return json([
            'countries' => Country::query()
                ->when($request->text, fn($query) => $query->whereFuzzy('name', $request->text))
                ->limit(10)
                ->get(),
        ]);
    }
}
