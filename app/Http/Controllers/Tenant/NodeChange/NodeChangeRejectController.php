<?php

namespace App\Http\Controllers\Tenant\NodeChange;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Models\NodeChange;
use App\Notifications\RequestStatusChanged;

class NodeChangeRejectController extends Controller
{
    public function __invoke(NodeChange $nodeChange)
    {
        $nodeChange->reject();

        // Send notification to the member who created the request
        if ($nodeChange->member) {
            $nodeChange->member->notify(new RequestStatusChanged($nodeChange, RequestStatus::rejected));
        }

        postHog(['event' => 'NodeModel Change: Rejected']);

        return back()->with(['success' => 'تم رفض التعديل بنجاح']);
    }
}
