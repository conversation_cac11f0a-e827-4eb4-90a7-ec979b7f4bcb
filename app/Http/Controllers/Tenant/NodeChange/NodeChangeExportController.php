<?php

namespace App\Http\Controllers\Tenant\NodeChange;

use App\Http\Controllers\Controller;
use App\Models\Node;
use App\Models\NodeChange;
use Rap2hpoutre\FastExcel\Facades\FastExcel;

class NodeChangeExportController extends Controller
{
    public function __invoke()
    {
        $nodeChanges = NodeChange::query()->with('node')->get();

        postHog(['event' => 'Export NodeModel Changes']);

        return FastExcel::data($nodeChanges)->download(
            'طلبات التعديلات.xlsx',
            fn(NodeChange $nodeChange) => [
                'الرقم' => $nodeChange->node?->id,
                'الاسم' => $nodeChange->node->name,
                'الجوال الحالي' => $nodeChange->node->mobile,
                'الجوال الجديد' => $nodeChange->new_attributes['mobile'],
                'الحالة' => $nodeChange->node->life_status_ar,
                'الجنس' => $nodeChange->node->gender->translate(),
                'حالة الطلب' => $nodeChange->status_ar,
            ]
        );
    }
}
