import * as Calendar from "expo-calendar";
import { Alert, Platform } from "react-native";
import colors from "./colors";

const getDefaultCalendarSource = async () => {
  const calendars = await Calendar.getCalendarsAsync(
    Calendar.EntityTypes.EVENT,
  );
  const defaultCalendars = calendars.filter(
    (each) =>
      each.source.name === (Platform.OS === "ios" ? "Default" : "Google"),
  );
  return defaultCalendars[0].source;
};

const getOrCreateCalendar = async () => {
  const calendars = await Calendar.getCalendarsAsync(
    Calendar.EntityTypes.EVENT,
  );

  let defaultCalendarId;

  if (Platform.OS === "ios") {
    defaultCalendarId = calendars.find((cal) => cal.allowsModifications)?.id;
  } else {
    defaultCalendarId = calendars.find(
      (cal) => cal.isPrimary && cal.allowsModifications,
    )?.id;
  }

  if (!defaultCalendarId) {
    defaultCalendarId = await Calendar.createCalendarAsync({
      title: "شجرة العائلة",
      color: colors.green["500"],
      entityType: Calendar.EntityTypes.EVENT,
      sourceId: (await getDefaultCalendarSource()).id,
      source: await getDefaultCalendarSource(),
      name: "شجرة العائلة",
      ownerAccount: "personal",
      accessLevel: Calendar.CalendarAccessLevel.OWNER,
    });
  }

  return defaultCalendarId;
};

type AddEventParams = {
  title: string;
  startDate: Date;
  endDate: Date;
  notes?: string;
};

export const addEventToCalendar = async ({
  title,
  startDate,
  endDate,
  notes,
}: AddEventParams) => {
  try {
    const { status } = await Calendar.requestCalendarPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("عذراً", "نحتاج إلى إذن للوصول إلى التقويم");
      return false;
    }

    const calendarId = await getOrCreateCalendar();

    await Calendar.createEventAsync(calendarId, {
      title,
      startDate,
      endDate,
      notes,
      timeZone: "Asia/Riyadh",
      alarms: [
        {
          relativeOffset: -60,
          method: Calendar.AlarmMethod.ALERT,
        },
      ],
    });

    Alert.alert("تم", "تمت إضافة الحدث إلى التقويم بنجاح", [{ text: "حسناً" }]);

    return true;
  } catch (error) {
    console.error("Calendar error:", error);
    Alert.alert("عذراً", "حدث خطأ أثناء إضافة الحدث إلى التقويم", [
      { text: "حسناً" },
    ]);
    return false;
  }
};
