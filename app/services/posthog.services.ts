import PostHog from "posthog-react-native";
import { Platform } from "react-native";
import DeviceInfo from "react-native-device-info";

export const postHog = new PostHog(
  "phc_CVPSRzgzGDrJZbYhLaMHm94BNxaQWdxBOiPe1yUr4HN",
  {
    host: "https://us.i.posthog.com",
    disabled: __DEV__,
  },
);

export const identifyUser = async (
  userId: string,
  userProperties?: Record<string, any>,
) => {
  postHog.identify(userId, {
    platform: Platform.OS,
    appVersion: DeviceInfo.getVersion(),
    ...userProperties,
  });
};

// Define event types as an object for type safety
export const EventType = {
  // Node-related events
  NODE_SEARCH: "node search",
  NODE_VIEW: "node view",
  RECENT_NODE_CLICK: "recent node click",
  CLEAR_RECENT_SEARCHES: "clear recent searches",

  // User actions
  USER_LOGIN: "user login",
  USER_LOGOUT: "user logout",
  USER_REGISTER: "user register",

  // Node management
  NODE_ADD: "node add",
  NODE_EDIT: "node edit",
  NODE_DELETE: "node delete",

  // Tree navigation
  TREE_VIEW: "tree view",
  TREE_ZOOM: "tree zoom",
  TREE_PAN: "tree pan",

  POST_VIEW: "post view",
  // Document events
  DOCUMENT_VIEW: "document view",
  DOCUMENT_SHARE: "document share",
} as const;

export type EventName = (typeof EventType)[keyof typeof EventType];

export const captureEvent = (
  eventName: EventName,
  properties?: Record<string, any>,
) => {
  try {
    postHog.capture(eventName, properties);
  } catch (error) {
    console.error(`Failed to capture event ${eventName}:`, error);
  }
};
