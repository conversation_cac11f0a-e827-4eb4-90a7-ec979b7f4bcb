import spacing from "@/app/config/spacing";
import { View } from "react-native";
import layout from "@/app/config/layout"; 
import React, { useMemo } from "react";
import { useSettingsQuery } from "@/app/services/common.services";
import { WhatsappIcon, SnapchatIcon, InstagramIcon, YoutubeIcon, XIcon } from "@hugeicons-pro/core-stroke-standard";
import { openUrl } from "@/app/helpers";
import AppText from "@/app/components/AppText";
import colors from "@/app/config/colors";

export const SocialMediaAccounts = () => {
  const { data } = useSettingsQuery();

  const accounts = useMemo(
    () =>
      data
        ? {
            whatsapp: data.find((s) => s.key === "whatsapp_link")?.value,
            snapchat: data.find((s) => s.key === "snapchat_link")?.value,
            twitter: data.find((s) => s.key === "twitter_link")?.value,
            instagram: data.find((s) => s.key === "instagram_link")?.value,
            youtube: data.find((s) => s.key === "youtube_link")?.value,
          }
        : null,
    [data],
  );

  const noAccounts = useMemo(
    () => !accounts || !Object.values(accounts).some((account) => account),
    [accounts],
  );

  if (noAccounts) {
    return null;
  }

  return (
    <View style={spacing.mt6}>
      <AppText
        textAlign="center"
        containerStyle={spacing.mb2}
        weight="medium"
        color={colors.gray["600"]}
      >
        حسابات العائلة
      </AppText>
      <View style={[layout.row, layout.justifyContent.center, layout.gap(18)]}>
        {accounts.whatsapp ? (
          <WhatsappIcon
            width={28}
            height={28}
            onPress={() => openUrl(accounts.whatsapp)}
          />
        ) : null}
        {accounts.snapchat ? (
          <SnapchatIcon
            width={28}
            height={28}
            onPress={() => openUrl(accounts.snapchat)}
          />
        ) : null}
        {accounts.twitter ? (
          <XIcon
            width={28}
            height={28}
            onPress={() => openUrl(accounts.twitter)}
          />
        ) : null}
        {accounts.instagram ? (
          <InstagramIcon
            width={28}
            height={28}
            onPress={() => openUrl(accounts.instagram)}
          />
        ) : null}

        {accounts.youtube ? (
          <YoutubeIcon
            width={28}
            height={28}
            onPress={() => openUrl(accounts.youtube)}
          />
        ) : null}
      </View>
    </View>
  );
};
