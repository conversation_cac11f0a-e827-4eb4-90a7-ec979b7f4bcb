import layout from "@/app/config/layout";
import AppShadow from "@/app/components/AppShadow";
import { AppImage } from "@/app/components/AppImage";
import rounded from "@/app/config/rounded";
import bg from "@/app/config/bg";
import { View } from "react-native";
import colors from "@/app/config/colors";
import AppText from "@/app/components/AppText";
import React from "react";
import useUser from "@/app/hooks/useUser";
import { Avatar } from "@/app/components/Avatar";
import AppButton from "@/app/components/AppButton";
import useAppNavigation, {
  useNavigateToNode,
} from "@/app/hooks/useAppNavigation";
import spacing from "@/app/config/spacing";
import { Bounceable } from "@/app/components/Bounceable";
import { useUnreadNotificationsCount } from "@/app/modules/Notifications/services/Notifications.services";
import borders from "@/app/config/borders";
import { Notification01Icon } from "@hugeicons-pro/core-stroke-standard";

export const Greetings = () => {
  const { user, isGuest } = useUser();
  const navigateToNode = useNavigateToNode();
  const navigation = useAppNavigation();
  const { data: unreadNotificationsCount } = useUnreadNotificationsCount();

  if (!user) {
    return null;
  }

  return (
    <View
      style={[
        layout.row,
        layout.justifyContent.spaceBetween,
        layout.alignItems.center,
      ]}
    >
      <View style={[layout.row, layout.alignItems.center, layout.gap(20)]}>
        {user.node?.photo_url ? (
          <AppShadow>
            <AppImage
              style={[{ width: 75, height: 75 }, rounded.full]}
              source={{ uri: user.node.photo_url }}
            />
          </AppShadow>
        ) : (
          <AppShadow
            containerStyle={[
              { width: 55, height: 55 },
              rounded.full,
              bg.white,
              layout.center,
            ]}
          >
            <View>
              <Avatar
                name={user.name ?? user.node?.name ?? ""}
                gender={user.node?.gender}
                size={55}
              />
            </View>
          </AppShadow>
        )}
        <View>
          <AppText size="md" weight="medium" color={colors.gray["500"]}>
            مرحبًا
          </AppText>
          <AppText size="xl" weight="semi-bold" color={colors.gray["800"]}>
            {user.name ?? user.node?.name}
          </AppText>
        </View>
      </View>
      <View
        style={[
          layout.row,
          spacing.px2,
          layout.gap(4),
          layout.alignItems.center,
        ]}
      >
        {user.node ? (
          <AppButton
            size="sm"
            type="outline"
            containerStyle={[spacing.mx2]}
            onPress={() => user.node && navigateToNode(user.node.id)}
          >
            صفحتي
          </AppButton>
        ) : null}
        {isGuest ? null : (
          <Bounceable
            onPress={() => navigation.navigate("NotificationsScreen")}
          >
            <Notification01Icon
              color={colors.green["600"]}
              width={24}
              height={24}
            />
            {unreadNotificationsCount > 0 ? (
              <View
                style={[
                  {
                    position: "absolute",
                    top: -16,
                    right: -16,
                    minWidth: 24,
                    height: 24,
                  },
                  bg.red["500"],
                  rounded.full,
                  layout.center,
                  borders.sm,
                  borders.white,
                ]}
              >
                <AppText size="xs" color="white" weight="bold">
                  {unreadNotificationsCount}
                </AppText>
              </View>
            ) : null}
          </Bounceable>
        )}
      </View>
    </View>
  );
};
