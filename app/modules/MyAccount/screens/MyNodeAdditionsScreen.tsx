import React from "react";
import { FlatList, View } from "react-native";
import { useNodeAdditions } from "../services/MyAccount.services";
import AppLayout from "@/app/components/AppLayout";
import Loader from "@/app/components/Loader";
import { Box } from "@/app/components/Box";
import AppText from "@/app/components/AppText";
import spacing from "@/app/config/spacing";
import layout from "@/app/config/layout";
import { Badge } from "@/app/components/Badge";
import colors from "@/app/config/colors";
import { NodeAdditionResource } from "@/app/http/resources/NodeAdditionResource";
import { formatDate } from "@/app/helpers";

const MyNodeAdditionsScreen = () => {
  const { nodeAdditions, isLoading, fetchNextPage, canFetchMore } =
    useNodeAdditions();

  if (isLoading && !nodeAdditions?.length) {
    return <Loader insideAppLayout />;
  }

  return (
    <AppLayout title="طلبات إضافة الأفراد">
      <FlatList
        data={nodeAdditions}
        renderItem={({ item }: { item: NodeAdditionResource }) => {
          // Determine status color
          const statusColor =
            item.status === "approved"
              ? "green"
              : item.status === "rejected"
                ? "red"
                : "yellow";

          return (
            <Box padding containerStyle={[spacing.mb3]}>
              <View
                style={[
                  layout.row,
                  layout.justifyContent.spaceBetween,
                  spacing.mb2,
                ]}
              >
                <AppText weight="bold" size="md">
                  طلب إضافة فرد
                </AppText>
                <Badge color={statusColor}>{item.status_ar}</Badge>
              </View>

              {item.parent && (
                <View style={[layout.row, spacing.mb2, layout.gap(2)]}>
                  <AppText color={colors.gray["600"]}>الأب: </AppText>
                  <AppText weight="medium">
                    {item.parent.full_name ?? item.parent.name}
                  </AppText>
                </View>
              )}

              <View style={[layout.row, layout.justifyContent.spaceBetween]}>
                <AppText color={colors.gray["600"]} size="sm">
                  تاريخ الطلب: {formatDate(item.created_at)}
                </AppText>
              </View>
            </Box>
          );
        }}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={[spacing.px3, spacing.py3]}
        onEndReached={() => canFetchMore && fetchNextPage()}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={
          <View style={[layout.center, spacing.mt6]}>
            <AppText color={colors.gray["600"]}>
              لا توجد طلبات إضافة أفراد
            </AppText>
          </View>
        }
      />
    </AppLayout>
  );
};

export default MyNodeAdditionsScreen;
