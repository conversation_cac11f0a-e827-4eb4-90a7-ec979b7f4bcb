<?php

namespace App\Notifications;

use App\Models\NodeAddition;
use App\Models\NodeChange;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Expo\ExpoMessage;

class RequestCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected NodeAddition|NodeChange $request) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['expo', 'database'];
    }

    /**
     * Get the Expo representation of the notification.
     */
    public function toExpo(object $notifiable): ExpoMessage
    {
        $body = $this->body();

        return ExpoMessage::create(
            'طلب ' . ($this->request instanceof NodeAddition ? 'إضافة' : 'تعديل') . ''
        )
            ->body($body)
            ->priority('high')
            ->playSound()
            ->data([
                'body' => $body,
                'request_id' => $this->request->id,
                'request_type' => $this->request instanceof NodeAddition ? 'addition' : 'change',
            ]);
    }

    public function toArray(): array
    {
        return [
            'body' => $this->body(),
            'request_id' => $this->request->id,
            'request_type' => $this->request instanceof NodeAddition ? 'addition' : 'change',
        ];
    }

    public function body(): string
    {
        $body =
            'تم إنشاء طلب ' .
            ($this->request instanceof NodeAddition ? 'إضافة' : 'تعديل') .
            ' الفرد';

        if ($this->request instanceof NodeAddition) {
            $nodeName = $this->request->node_attributes['name'] ?? 'غير معروف';
            $body .= " {$nodeName}";
        } else {
            $nodeName = $this->request->node->name ?? 'غير معروف';
            $body .= " {$nodeName}";
        }
        return $body;
    }
}
