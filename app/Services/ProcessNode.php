<?php

namespace App\Services;

use App\Enums\Gender;
use App\Jobs\UpdateFullNameJob;
use App\Models\Line;
use App\Models\Node;
use App\Models\Relationship;
use App\Models\Tenant;
use Arr;
use DB;
use Storage;
use function collect;

class ProcessNode
{
    public static function handle(array $data, ?Node $node = null)
    {
        $data = Arr::only($data, Node::EDITABLE_COLUMNS);

        return DB::transaction(function () use ($node, $data) {
            ['node' => $node, 'line' => $line] = $node ? self::update($node, $data) : self::create($data);

            if (isset($data['relationships'])) {
                self::processRelationships($node, $data['relationships']);
            }

            return [
                'node' => $node,
                'line' => $line,
            ];
        });
    }

    public static function create(array $data, Tenant $tenant = null): array
    {
        $tenant = $tenant ?? tenant();

        $initialStyle = self::initialNodeStyle(Node::find($data['parent_id']));

        $node = Node::create([
            'style' => $initialStyle,
            'branch_id' => $tenant->getMainBranch()->id,
            'tenant_id' => $tenant->id,
            'bg_color' => $data['bg_color'] ?? Gender::from($data['gender'])->color(),
            ...Arr::except($data, ['relationships', 'bg_color']),
        ]);

        if ($node->parent_id) {
            $line = Line::create([
                'from_node_id' => $node->parent_id,
                'to_node_id' => $node->id,
                'tenant_id' => $node->tenant_id,
                'points' => self::initialLineStyle($initialStyle),
            ]);
        }

        $node->updateFullName();

        return [
            'node' => $node->refresh(),
            'line' => $line ?? null,
        ];
    }

    public static function update(Node $node, array $data): array
    {
        if (isset($data['photo']) && $node->photo) {
            Storage::delete($node->photo);
        }

        $hasNameOrGenderChanged = self::hasNameOrGenderChanged($node, $data);

        $node->update([
            'bg_color' => $data['bg_color'] ?? ($node->bg_color ?? Gender::from($data['gender'])->color()),
            ...Arr::except($data, ['relationships', 'bg_color']),
            'added_to_paper_at' => $hasNameOrGenderChanged ? null : $node->added_to_paper_at,
        ]);

        if ($hasNameOrGenderChanged) {
            $node->descendantsAndSelf()->each(fn(Node $n) => UpdateFullNameJob::dispatch($n));
        }

        return [
            'node' => $node,
            'line' => null,
        ];
    }

    public static function initialNodeStyle(?Node $parent): ?array
    {
        if (!$parent) {
            return [
                'x' => 0,
                'y' => 0,
            ];
        }

        $basePosition = [
            'x' => $parent->style['x'] ?? 0,
            'y' => $parent->style['y'] ?? 0,
        ];

        return [
            'x' => $basePosition['x'] - random_int(-100, 100),
            'y' => $basePosition['y'] - random_int(0, 100),
        ];
    }

    public static function initialLineStyle(array $style): array
    {
        return [
            // only one point for now
            ['x' => $style['x'], 'y' => $style['y']],
        ];
    }

    public static function hasNameOrGenderChanged(?Node $node, array $data): bool
    {
        if (!isset($data['name']) || !isset($data['gender'])) {
            return false;
        }

        $oldName = $node?->name;
        $oldGender = $node?->gender;

        $newName = $data['name'];
        $newGender = $data['gender'];

        return $node && ($oldName !== $newName || $oldGender !== $newGender);
    }

    public static function processRelationships(Node $node, array $relationships): void
    {
        collect($relationships)->each(function ($rawRelationship) use ($node) {
            $nodeId = $rawRelationship['node_id'] ?? null;

            if ($rawRelationship['is_new']) {
                Relationship::create([
                    'husband_id' => $node->gender === Gender::Male ? $node->id : $nodeId,
                    'wife_id' => $node->gender === Gender::Female ? $node->id : $nodeId,
                    'name' => $nodeId ? null : $rawRelationship['name'],
                    'family_name' => $nodeId ? null : $rawRelationship['family_name'],
                    'status' => $rawRelationship['status'],
                    'tenant_id' => $node->tenant_id,
                ]);

                return;
            }

            if ($rawRelationship['is_deleted']) {
                Relationship::find($rawRelationship['id'])->delete();

                return;
            }

            Relationship::findOrFail($rawRelationship['id'])->update([
                'husband_id' => $node->gender === Gender::Male ? $node->id : $nodeId,
                'wife_id' => $node->gender === Gender::Female ? $node->id : $nodeId,
                'name' => $nodeId ? null : $rawRelationship['name'],
                'family_name' => $nodeId ? null : $rawRelationship['family_name'],
                'status' => $rawRelationship['status'],
            ]);
        });
    }
}
