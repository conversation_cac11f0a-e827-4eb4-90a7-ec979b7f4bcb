import React, { useMemo } from "react";
import { View } from "react-native";

import colors from "../config/colors";
import { Node } from "@/app/services/common.services";
import layout from "@/app/config/layout";
import AppText from "@/app/components/AppText";
import { Male02Icon, Female02Icon } from "@hugeicons-pro/core-stroke-standard";
import { Badge } from "@/app/components/Badge";
import HR from "@/app/components/HR";
import { formatFirstThreeNames } from "@/app/helpers";

type Props = {
  node: Node;
};

export const NodeBasicInfo: React.FC<Props> = ({ node }) => {
  const formattedName = useMemo(
    () => node && formatFirstThreeNames(node),
    [node],
  );

  const depth = useMemo(() => node?.full_name?.split(/بن|بنت/g).length, [node]);

  return (
    <>
      <View style={[layout.row, layout.justifyContent.spaceBetween]}>
        <View>
          <AppText weight="bold" size="md">
            {formattedName}
          </AppText>
          {depth ? (
            <AppText color={colors.gray["600"]}>{`المستوى ${depth}`}</AppText>
          ) : null}
        </View>
        <View style={[layout.row, layout.gap(12), layout.alignItems.center]}>
          {node.gender === "male" ? (
            <Male02Icon width={28} height={28} color={colors.blue["700"]} />
          ) : (
            <Female02Icon width={28} height={28} color={colors.pink["700"]} />
          )}
          <Badge size="sm" color="gray">
            {node.life_status_ar}
          </Badge>
        </View>
      </View>
      <HR size={25} />
      <View style={[layout.gap(6)]}>
        <View style={[layout.row, layout.gap(12)]}>
          <AppText color={colors.gray["600"]}>الاسم</AppText>
          <AppText weight="medium">{node.full_name}</AppText>
        </View>
        <View style={[layout.row, layout.gap(12)]}>
          <AppText color={colors.gray["600"]}>الجوال</AppText>
          <AppText weight="medium">{node.mobile}</AppText>
        </View>
        <View style={[layout.row, layout.gap(12)]}>
          <AppText color={colors.gray["600"]}>البريد الإلكتروني</AppText>
          <AppText weight="medium">{node.email}</AppText>
        </View>
      </View>
    </>
  );
};
