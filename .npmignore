# Source files
src/
.changeset/

# Development files
*.test.ts
*.test.js
.eslintrc.json
.prettierrc
tsconfig.json
vitest.config.ts

# Development directories
.github/
node_modules/
coverage/

# Environment and config
.env
.env.local
.env.*.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Build artifacts (keep dist and registry)
# dist/ - needed for CLI
# registry/ - needed for utilities