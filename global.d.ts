import type { route as routeFn } from 'ziggy-js';

declare global {
  const route: typeof routeFn;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const gtag: any;
  const ReactNativeWebView: {
    postMessage: (message: string) => void;
  };
}

// This import is necessary for module augmentation.
// It allows us to extend the 'Props' interface in the 'react-select/base' module
// and add our custom property 'myCustomProp' to it.
export {};

declare module 'react-select/base' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  export interface Props<Option, IsMulti extends boolean, Group extends GroupBase<Option>> {
    label: string;
    error: string;
    containerClassName: string;
  }
}
