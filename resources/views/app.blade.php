<!DOCTYPE html>
<html style='height: 100vh;' lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1.0, user-scalable=no">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <meta name="apple-itunes-app" content="app-id=6683282889" />
  <meta name="apple-pay-domain-association" content="apple-developer-merchantid-domain-association">
  <meta property="og:title" content="أوراق لشجرة العائلة">
  <meta property="og:description"
        content="أوراق لتصميم شجرة العائلة الإلكترونية والورقية، صمم شجرة عائلتك، وشاركها مع من تحب من الأهل والأصدقاء.🌳">
  <meta property="og:image" content="https://awraq.app/mail/logo.png">
  <meta property="og:url" content="https://awraq.app">

  <meta name="twitter:title" content="أوراق لشجرة العائلة">
  <meta name="twitter:description"
        content="أوراق لتصميم شجرة العائلة الإلكترونية والورقية، صمم شجرة عائلتك، وشاركها مع من تحب من الأهل والأصدقاء.🌳">
  <meta name="twitter:url" content="https://awraq.app">
  <meta name="twitter:card"
        content="أوراق لتصميم شجرة العائلة الإلكترونية والورقية، صمم شجرة عائلتك، وشاركها مع من تحب من الأهل والأصدقاء.🌳">

  @if(request()->getHost() !== 'tree.alrashoodi.sa')
    <meta name="description"
          content="أوراق لتصميم شجرة العائلة الإلكترونية والورقية، صمم شجرة عائلتك، وشاركها مع من تحب من الأهل والأصدقاء.🌳">
  @endif

  <title inertia>{{ config('app.name', 'أوراق') }}</title>
  <link rel="icon" href="{{ asset('mail/logo.png') }}">

  @routes

  @viteReactRefresh
  @vite(['resources/js/app.tsx', "resources/js/pages/{$page['component']}.tsx"])
  @inertiaHead

  <!-- Moyasar Scripts -->
  <script src="https://cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?version=4.8.0&features=fetch"></script>
  <script src="https://cdn.moyasar.com/mpf/1.15.0/moyasar.js"></script>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-DZGCE7C5TF">
  </script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }

    gtag('js', new Date());

    gtag('config', 'G-DZGCE7C5TF');
  </script>
</head>
<body class="font-sans antialiased">
@inertia
</body>

</html>
