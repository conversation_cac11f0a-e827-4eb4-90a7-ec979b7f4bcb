import { Location } from '@/services/location.services';
import { NodeModel, Relationship } from '@/types/models';
import { router } from '@inertiajs/react';
import dayjs from 'dayjs';
import * as hijri from 'hijri-js';
import { toast as sonner } from 'sonner';

export const toast = (message: string, type: 'success' | 'error' = 'success') => {
  if (type === 'success') {
    sonner.success(message);
  } else {
    sonner.error(message);
  }
};

export const isSlug = (word: string) => /[a-z]|-/.test(word);

export const debounce = <T extends (...args: unknown[]) => void>(fn: T, delay: number) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => fn(...args), delay);
  };
};

const translations = {
  'show-branch': 'استعراض شجرة',
  'edit-branch': 'تعديل شجرة',
  'manage-users': 'التحكم بالمستخدمين',
  'manage-settings': 'التحكم بالإعدادات',
  'manage-statistics': 'مشاهدة الإحصائيات',
  family_name: 'اسم العائلة',
  created: 'إنشاء',
  updated: 'تعديل',
  deleted: 'حذف',
  restored: 'استعادة',
  name: 'الاسم',
  life_status: 'حي أو متوفى',
  birth_date: 'تاريخ الولادة',
  death_date: 'تاريخ الوفاة',
  mobile: 'رقم الجوال',
  gender: 'الجنس',
  about: 'النبذة',
  full_name: 'الاسم الكامل',
  nickname: 'اللقب',
  marriage: 'زواج',
  divorce: 'طلاق',
  widow: 'أرمل',
} as const;

type TranslationKey = keyof typeof translations;

export const trans = (word: TranslationKey | string, defaultWord: string | null = null): string => {
  return translations[word as TranslationKey] ?? defaultWord ?? word;
};

const h = hijri.initialize();

export const formatHijriDate = (date: string) => h.toHijri(dayjs(date).format('DD/MM/YYYY'), '/').plain;

export const visitNode = (nodeId: number) => {
  router.visit(route('branches.show', nodeId), {
    only: ['currentNode'],
  });
};

export const formatLocation = ({ country, city, district }: Location) => {
  let location = '';

  if (country) {
    location += country.name;
  }

  if (city) {
    location += ` - ${city.name}`;
  }

  if (district) {
    location += ` - ${district.name}`;
  }

  return location;
};

export function calculateDescendantsCount(node: NodeModel): number {
  if (!node.children || node.children.length === 0) {
    return 0;
  }

  let count = node.children.length;
  for (const child of node.children) {
    count += calculateDescendantsCount(child);
  }
  return count;
}

type RawNodeWithDepth = NodeModel & { depth: number; descendants_count: number; children?: RawNodeWithDepth[] };

export function arrayToTree(items: RawNodeWithDepth[]): RawNodeWithDepth {
  const itemMap = new Map<number, RawNodeWithDepth>();
  const roots: RawNodeWithDepth[] = [];

  // First pass: create nodes map
  for (const item of items) {
    // Create a new object with children array and initial depth
    itemMap.set(item.id, {
      ...item,
      children: [],
      depth: 0,
      descendants_count: 0,
    });
  }

  // Second pass: establish relationships and calculate depth
  for (const item of itemMap.values()) {
    if (item.parent_id === null) {
      roots.push(item);
    } else {
      const parent = itemMap.get(item.parent_id);
      if (parent) {
        item.depth = parent.depth + 1; // Set depth based on parent
        parent.children?.push(item);
      }
    }
  }

  const root = roots[0];
  if (root) {
    // Calculate descendants count for all nodes starting from root
    root.descendants_count = calculateDescendantsCount(root);
    const stack = [...(root.children || [])];

    while (stack.length > 0) {
      const node = stack.pop()!;
      node.descendants_count = calculateDescendantsCount(node);
      if (node.children) {
        stack.push(...node.children);
      }
    }
  }

  return root;
}

export const truncate = (string: string, length = 50) =>
  string?.length > length ? string.substring(0, length) + '...' : string;

export const ordinalsAr = (num: number, isFeminine = false): string => {
  const feminineSuffix = 'ة';

  const specialNums: { [key: number]: { masculine: string; feminine: string } } = {
    1: {
      masculine: 'الأول',
      feminine: 'الأولى',
    },
    10: {
      masculine: 'العاشر',
      feminine: 'العاشرة',
    },
  };

  const lastDigits: { [key: number]: string } = {
    10: 'عشر',
    20: 'العشرون',
    30: 'الثلاثون',
    40: 'الأربعون',
    50: 'الخمسون',
    60: 'الستون',
    70: 'السبعون',
    80: 'الثمانون',
    90: 'التسعون',
    // you have to add the rest here
  };

  const firstDigits: { [key: number]: string } = {
    1: 'الحادي',
    2: 'الثاني',
    3: 'الثالث',
    4: 'الرابع',
    5: 'الخامس',
    6: 'السادس',
    7: 'السابع',
    8: 'الثامن',
    9: 'التاسع',
  };

  if (specialNums[num]) return isFeminine ? specialNums[num]['feminine'] : specialNums[num]['masculine'];

  if (firstDigits[num]) return isFeminine ? firstDigits[num] + feminineSuffix : firstDigits[num];

  if (lastDigits[num]) return lastDigits[num];

  const firstDigit = num % 10;
  const lastDigit = num - firstDigit;

  if (isFeminine) {
    if (lastDigit < 20) return firstDigits[firstDigit] + feminineSuffix + ' ' + lastDigits[lastDigit] + feminineSuffix;
    else return firstDigits[firstDigit] + feminineSuffix + ' و' + lastDigits[lastDigit];
  } else {
    if (lastDigit < 20) return firstDigits[firstDigit] + ' ' + lastDigits[lastDigit];
  }

  return firstDigits[firstDigit] + ' و' + lastDigits[lastDigit];
};

export const toEnglishDigits = (str: string) => {
  let e = '۰'.charCodeAt(0);
  str = str.replace(/[۰-۹]/g, (t) => (t.charCodeAt(0) - e).toString());

  e = '٠'.charCodeAt(0);
  str = str.replace(/[٠-٩]/g, (t) => (t.charCodeAt(0) - e).toString());

  return str.trim();
};

export const formatFirstThreeNames = (node: NodeModel) => {
  return (
    node.full_name
      ?.split(/ (بن|بنت) /)
      .slice(0, 5)
      .join(' ') ?? node.name
  );
};

export const otherParentInfo = (node: NodeModel, rawRelationship: Relationship | null | undefined = null) => {
  rawRelationship ??= node.other_parent_relationship;

  if (!rawRelationship) {
    return {
      id: null,
      label: '',
      name: '',
    };
  }

  const husbandOrWifeNode = node.gender === 'male' ? rawRelationship.wife : rawRelationship.husband;

  const otherParentLabel = node.gender === 'male' ? 'الأم' : 'الأب';

  if (!husbandOrWifeNode) {
    return {
      id: rawRelationship.id,
      label: otherParentLabel,
      name: `${rawRelationship.name} ${rawRelationship.family_name}`,
    };
  }

  return {
    id: rawRelationship.id,
    label: otherParentLabel,
    name: husbandOrWifeNode.name,
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const debounceReload = debounce((data: any) => router.reload({ data }), 350);
