import Card from '@/components/ui/Card';
import { NodeModel, Relationship } from '@/types/models';
import { trans } from '@/utils/helpers';
import { useMemo } from 'react';

type NodeRelationshipsProps = {
  node: NodeModel;
  editable?: boolean;
};

export function NodeRelationships({ node }: NodeRelationshipsProps) {
  const relationships = useMemo(() => {
    return node.gender === 'male' ? node.wives : node.husbands;
  }, [node.gender, node.wives, node.husbands]);

  const nodeName = (relationship: Relationship) => {
    if (relationship.name && relationship.family_name) {
      return `${relationship.name} ${relationship.family_name}`;
    }

    return node.gender === 'male' ? relationship.wife?.name : relationship.husband?.name;
  };

  return (
    <Card title="الأزواج">
      {relationships?.length ? (
        <div>
          <ul role="list" className="divide-y divide-gray-200">
            {relationships.map((relationship) => (
              <li key={relationship.id} className="flex justify-between py-4">
                <div>
                  <p className="text-sm font-bold text-gray-900">{nodeName(relationship)}</p>
                  <p className="text-sm text-gray-500">{trans(relationship.status)}</p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div>
          <div className="mx-auto flex w-full flex-col items-center justify-center rounded-md text-center text-sm font-bold text-gray-300">
            <span>لا يوجد أزواج</span>
          </div>
        </div>
      )}
    </Card>
  );
}

export default NodeRelationships;
