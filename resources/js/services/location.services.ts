import axios from 'axios';

export type Country = {
  id: number;
  name: string;
};

export type City = {
  id: number;
  name: string;
};

export type District = {
  id: number;
  name: string;
};

export type Location = { country: Country | null; city: City | null; district: District | null };

export const fetchCountries = async (text: string) => {
  const { data } = await axios.get<{ data: { countries: Country[] } }>(route('countries.index', { text }));

  return data.data.countries.map((country) => ({
    value: country.id,
    label: country.name,
    ...country,
  }));
};

export const fetchCities = async (text: string) => {
  if (!text) {
    return [];
  }

  const { data } = await axios.get<{ data: { cities: City[] } }>(route('cities.index', { text }));

  return data.data.cities.map((city) => ({ value: city.id, label: city.name, ...city }));
};

export const fetchDistricts = async (text: string, cityId: number | null | undefined) => {
  if (!text || !cityId) {
    return [];
  }

  const { data } = await axios.get<{ data: { districts: District[] } }>(
    route('districts.index', {
      city_id: cityId,
      text,
    }),
  );

  return data.data.districts.map((district) => ({
    value: district.id,
    label: district.name,
    ...district,
  }));
};
