import { Link } from '@inertiajs/react';
import clsx from 'clsx';
import { ReactNode } from 'react';

type NavLinkProps = {
    href?: string;
    active?: boolean;
    children: ReactNode;
};

export default function NavLink({ href, active = false, children }: NavLinkProps) {
    const classes = clsx(
        'mx-2 inline-flex items-center border-b-2 pt-1 text-sm leading-5 font-medium transition',
        active
            ? 'border-green-400 text-gray-900 focus:border-green-700'
            : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 focus:border-gray-300 focus:text-gray-700',
    );

    if (href) {
        return (
            <Link href={href} className={classes}>
                {children}
            </Link>
        );
    }

    return <button className={classes}>{children}</button>;
}
