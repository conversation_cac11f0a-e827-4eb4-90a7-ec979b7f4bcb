import { NodeModel } from '@/types/models';
import { Edge, Node, Position } from '@xyflow/react';

const nodeWidth = 150;
const nodeHeight = 36;

export type NodeFactoryProps = {
  direction?: 'TB' | 'LR';
  onClickNode?: (node: NodeModel) => void;
  editable?: boolean;
  isRoot?: boolean;
};

export const createReactFlowNode = (
  nodeModel: NodeModel,
  options: NodeFactoryProps & { position: { x: number; y: number } },
): Node => {
  const { direction = 'TB', onClickNode, editable = false, isRoot = false, position } = options;
  const isTreeHorizontal = direction === 'LR';

  return {
    id: nodeModel.id.toString(),
    data: { data: nodeModel, direction, onClickNode, editable, isRoot },
    type: 'baseNode',
    width: nodeWidth,
    height: nodeHeight,
    position,
    sourcePosition: isTreeHorizontal ? Position.Right : Position.Bottom,
    targetPosition: isTreeHorizontal ? Position.Left : Position.Top,
  };
};

export const createReactFlowEdge = (
  sourceId: string,
  targetId: string,
  options: { direction?: 'TB' | 'LR'; label?: string } = {},
): Edge => {
  const { direction = 'TB', label } = options;
  const isTreeHorizontal = direction === 'LR';

  return {
    id: `e${sourceId}-${targetId}`,
    source: sourceId,
    target: targetId,
    sourceHandle: isTreeHorizontal ? Position.Right : Position.Bottom,
    targetHandle: isTreeHorizontal ? Position.Left : Position.Top,
    label,
    labelShowBg: false,
    labelStyle: {
      textShadow: `
        2px 2px 0 #fff, -2px 2px 0 #fff, 2px -2px 0 #fff, -2px -2px 0 #fff,
        2px 0 0 #fff, -2px 0 0 #fff, 0 2px 0 #fff, 0 -2px 0 #fff
      `,
    },
    type: 'smoothstep',
  };
};
