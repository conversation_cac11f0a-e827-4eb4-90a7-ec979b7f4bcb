import { BaseNode } from '@/components/base-node';
import {
  NodeHeader,
  NodeHeaderA<PERSON>,
  NodeHeaderIcon,
  NodeHeaderMenuAction,
  NodeHeaderTitle,
} from '@/components/node-header';
import { NodeTools } from '@/components/tree/NodeTools';
import { Button } from '@/components/ui/button';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useStreamedNodes } from '@/hooks/use-streamed-nodes';
import { NodeModalType, useNodeModalStore } from '@/store/node-modal';
import { Node } from '@/types/models';
import { Female02Icon, Male02Icon, PlusSignIcon, ViewIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { router } from '@inertiajs/react';
import { Handle, NodeProps, Position, useStore } from '@xyflow/react';
import { memo, useCallback } from 'react';
import fastMemo from 'react-fast-memo';

const zoomSelector = (s) => s.transform[2] >= 0.4;

export const nodeTypes = {
  baseNode: memo((props: NodeProps) => {
    const showContent = useStore(zoomSelector);

    const setNodeModal = useNodeModalStore((s) => s.setNodeModal);
    const { data: reactFlowData } = props;

    const node = reactFlowData.data as Node;

    const isTreeHorizontal = reactFlowData.direction === 'LR';

    const isRootNode = reactFlowData?.isRoot;
    const hasChildren = !!reactFlowData?.children?.length;

    const onClickAdd = useCallback(() => setNodeModal(NodeModalType.NODE_INFO, node), [setNodeModal, node]);

    return (
      <BaseNode
        selected={props.selected}
        className="relative px-2 py-1 pb-0"
        dir="rtl"
        style={{
          borderTop: showContent ? `4px solid ${node.bg_color}` : `50px solid ${node.bg_color}`,
          boxShadow: '0 5px 15px rgba(0,0,0,0.08), 0 2px 5px rgba(0,0,0,0.05)',
          transform: `scale(${node.style?.scale || 1})`,
        }}
      >
        {props.selected ? <NodeTools node={node} /> : null}
        {/* Glow effect */}
        <div
          className="pointer-events-none absolute inset-0 opacity-10"
          style={{
            background: `radial-gradient(ellipse at center, ${node.bg_color} 0%, transparent 100%)`,
          }}
        />
        {showContent ? <NodeContent node={node} /> : null}

        {/* Renders an invisible handle for nodes that have children */}
        {hasChildren && (
          <Handle
            className="invisible"
            type="source"
            position={isTreeHorizontal ? Position.Right : Position.Bottom}
            id={isTreeHorizontal ? Position.Right : Position.Bottom}
          />
        )}

        {(isRootNode || props.selected) && !hasChildren && (
          <div className="flex justify-center">
            <Button size="xs" className="mb-2 w-full" onClick={onClickAdd}>
              <HugeiconsIcon icon={PlusSignIcon} size={8} />
              <span className="text-[9px]">اضافة فرد</span>
            </Button>
          </div>
        )}

        {/* Target Handle */}
        {!isRootNode && (
          <Handle
            className="invisible"
            type="target"
            position={isTreeHorizontal ? Position.Left : Position.Top}
            id={isTreeHorizontal ? Position.Left : Position.Top}
          />
        )}
      </BaseNode>
    );
  }),
};

export const NodeContent = fastMemo(({ node }: { node: Node }) => {
  const { removeNode } = useStreamedNodes();
  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  let textSize = 10;
  if (node.name.length > 7) {
    textSize = 9;
  }

  const onClickView = useCallback(() => setNodeModal(NodeModalType.NODE_INFO, node), [setNodeModal, node]);

  const onClickRemove = useCallback(() => {
    if (!confirm('هل أنت متأكد؟')) {
      return;
    }

    router.delete(route('nodes.destroy', node.id), {
      onSuccess: () => {
        removeNode(node.id);
      },
    });
  }, [node.id, removeNode]);

  return (
    <NodeHeader>
      <NodeHeaderIcon>
        {node.gender === 'male' ? (
          <HugeiconsIcon size={12} icon={Male02Icon} className="text-blue-500" />
        ) : (
          <HugeiconsIcon size={12} icon={Female02Icon} className="text-pink-500" />
        )}
      </NodeHeaderIcon>
      <NodeHeaderTitle className="w-full truncate text-nowrap" style={{ fontSize: `${textSize}px` }}>
        {node.name}
      </NodeHeaderTitle>
      <NodeHeaderActions>
        <Button variant="ghost" size="icon" onClick={onClickView} className="-mx-2">
          <div className="flex flex-col justify-center">
            <HugeiconsIcon size={14} icon={ViewIcon} className="text-gray-600" />
            <span className="-mt-0.5 text-[5.5px] font-semibold text-gray-600">عرض</span>
          </div>
        </Button>
        <NodeHeaderMenuAction label="">
          {/*<DropdownMenuSeparator />*/}
          <DropdownMenuItem variant="destructive" onClick={onClickRemove}>
            حذف
          </DropdownMenuItem>
        </NodeHeaderMenuAction>
      </NodeHeaderActions>
    </NodeHeader>
  );
});
