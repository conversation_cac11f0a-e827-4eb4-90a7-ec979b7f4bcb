import { NodeModel } from '@/types/models';
import { Edge, Node, Position } from '@xyflow/react';
import { layoutFromMap } from 'entitree-flex';

const Orientation = {
  Vertical: 'vertical',
  Horizontal: 'horizontal',
} as const;

const nodeWidth = 150;
const nodeHeight = 36;

type Props = {
  tree: NodeModel[];
  direction?: string;
  preferNodePosition?: boolean;
};

export const layoutElements = ({
  tree,
  direction = 'TB',
  preferNodePosition = false,
}: Props): { nodes: NodeModel[]; edges: Edge[] } => {
  const isTreeHorizontal = direction === 'LR';

  const entitreeSettings = {
    clone: true, // returns a copy of the input, if your application does not allow editing the original object
    enableFlex: true, // has slightly better performance if turned off (node.width, node.height will not be read)
    firstDegreeSpacing: isTreeHorizontal ? 40 : -10, // spacing in px between nodes belonging to the same source, e.g. children with same parent
    nextAfterAccessor: 'spouses', // the side node prop used to go sideways, AFTER the current node
    nextAfterSpacing: 50, // the spacing of the "side" nodes AFTER the current node
    nextBeforeAccessor: 'siblings', // the side node prop used to go sideways, BEFORE the current node
    nextBeforeSpacing: 50, // the spacing of the "side" nodes BEFORE the current node
    nodeHeight, // default node height in px
    nodeWidth, // default node width in px
    orientation: Orientation.Vertical, // "vertical" to see parents top and children bottom, "horizontal" to see parents left and
    rootX: 0, // set root position if other than 0
    rootY: 0, // set root position if other than 0
    secondDegreeSpacing: isTreeHorizontal ? 40 : -10, // spacing in px between nodes not belonging to same parent eg "cousin" nodes
    sourcesAccessor: 'parents', // the prop used as the array of ancestors ids
    sourceTargetSpacing: 50, // the "vertical" spacing between nodes in vertical orientation, horizontal otherwise
    targetsAccessor: 'children', // the prop used as the array of children ids
  } as const;

  const root = Object.values(tree).find((node) => node.is_root)!;

  const { nodes: entitreeNodes, rels: entitreeEdges } = layoutFromMap(root.id, formatTreeData(tree), {
    ...entitreeSettings,
    orientation: isTreeHorizontal ? Orientation.Horizontal : Orientation.Vertical,
  });

  const nodes: NodeModel[] = [],
    edges: Edge[] = [];

  entitreeEdges.forEach((edge) => {
    const sourceNode = edge.source.id.toString();
    const targetNode = edge.target.id.toString();

    const newEdge: Edge = {
      id: 'e' + sourceNode + targetNode,
      source: sourceNode,
      target: targetNode,
      sourceHandle: isTreeHorizontal ? Position.Right : Position.Bottom,
      targetHandle: isTreeHorizontal ? Position.Left : Position.Top,
      label: edge.target.data.other_parent_relationship?.name,
      labelShowBg: false,
      labelStyle: {
        //thick white border on text
        textShadow: `
        2px 2px 0 #fff,
    -2px 2px 0 #fff,
    2px -2px 0 #fff,
    -2px -2px 0 #fff,
    2px 0 0 #fff,
    -2px 0 0 #fff,
    0 2px 0 #fff,
    0 -2px 0 #fff
        `,
      },
    };

    edges.push(newEdge);
  });

  entitreeNodes.forEach((node) => {
    const newNode: Node = {
      id: node.id.toString(),
      data: { direction, isRoot: false, ...node },
      type: 'baseNode',
      width: nodeWidth,
      height: nodeHeight,
      position: {
        x: preferNodePosition ? (node.data.style?.x ?? node.x) : node.x,
        y: preferNodePosition ? (node.data.style?.y ?? node.y) : node.y,
      },
      sourcePosition: isTreeHorizontal ? Position.Right : Position.Bottom,
      targetPosition: isTreeHorizontal ? Position.Left : Position.Top,
    };

    nodes.push(newNode);
  });

  return { nodes, edges };
};

const formatTreeData = (
  data: NodeModel[],
): Record<
  number,
  {
    id: number;
    is_root: boolean;
    data: Node;
    children: number[];
  }
> => {
  // First pass: create the base structure and build parent-to-children mapping
  const result: Record<
    number,
    {
      id: number;
      is_root: boolean;
      data: Node;
      children: number[];
    }
  > = {};

  const childrenMap = new Map<number, number[]>();

  // Single pass through data to build both structures
  for (const node of data) {
    // Initialize result entry
    result[node.id] = {
      id: node.id,
      is_root: node.is_root,
      data: node,
      children: [],
    };

    // Build parent-to-children mapping
    if (node.parent_id !== null && node.parent_id !== undefined) {
      if (!childrenMap.has(node.parent_id)) {
        childrenMap.set(node.parent_id, []);
      }
      childrenMap.get(node.parent_id)!.push(node.id);
    }
  }

  // Second pass: assign children arrays
  for (const nodeId in result) {
    const id = parseInt(nodeId, 10);
    result[id].children = childrenMap.get(id) || [];
  }

  return result;
};
