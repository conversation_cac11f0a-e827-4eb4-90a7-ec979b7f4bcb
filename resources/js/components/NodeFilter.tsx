import { BallBeatSpinner } from '@/components/loaders/BallBeatSpinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RawNode } from '@/lib/designed-tree/components/create-node';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { ReactNode } from 'react';
import { useLocalStorage } from 'react-use';
import { useDebounce } from 'use-debounce';

const fetchNodes = async (url: string, text: string, urlParams: Record<string, unknown>): Promise<RawNode[]> => {
  const response = await axios({
    url: url,
    method: 'GET',
    params: {
      text: text.replace(/بن|بنت/g, ''),
      ...urlParams,
    },
  });

  return response.data.data.nodes;
};

type Props = {
  url?: string;
  urlParams?: Record<string, unknown>;
  resetAfterSelect?: boolean;
  disabled?: boolean;
  onSelect: (node: RawNode) => void;
  children?: ReactNode;
};

export function NodeFilter({
  url = route('nodes.filter.index'),
  urlParams = {},
  resetAfterSelect = false,
  disabled = false,
  onSelect,
  children,
}: Props) {
  const [filterText = '', setFilterText] = useLocalStorage('node-filter-text', '');
  const [debouncedFilterText] = useDebounce(filterText, 350);

  const { data: filteredNodes = [], isFetching } = useQuery({
    queryKey: ['nodes', 'filter', debouncedFilterText, url, urlParams],
    queryFn: () => fetchNodes(url, debouncedFilterText, urlParams),
    enabled: !!debouncedFilterText.trim(),
    placeholderData: (previousData) => previousData,
  });

  const handleSelect = (node: RawNode) => {
    onSelect(node);

    if (resetAfterSelect) {
      setFilterText('');
    }
  };

  return (
    <div className="mt-4 flex flex-col">
      <div className="flex flex-wrap gap-4">
        <Input
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          disabled={disabled}
          placeholder="بحث..."
          helper="بحث بالاسم الكامل أو رقم الجوال أو الرقم التسلسلي"
        />

        {children}
      </div>

      {(isFetching || filteredNodes.length > 0) && (
        <div className="flex flex-col">
          {isFetching && (
            <div className="mx-auto my-2">
              <BallBeatSpinner color="#10B981" />
            </div>
          )}
          {filteredNodes.length > 0 && (
            <div className="flex flex-col flex-wrap">
              <div className="w-full">
                {filteredNodes.map((node) => (
                  <Button key={node.id} variant="link" type="button" onClick={() => handleSelect(node)}>
                    {node.full_name}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default NodeFilter;
