import React from 'react';

interface MobileAppLinksProps {
  className?: string;
}

const MobileAppLinks: React.FC<MobileAppLinksProps> = ({ className = '' }) => {
  return (
    <div className={`flex flex-wrap justify-center gap-4 ${className}`}>
      <a 
        href="https://apps.apple.com/us/app/family-tree-app/id123456789" 
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center justify-center rounded-lg bg-black px-5 py-3 transition-all hover:bg-gray-900"
      >
        <div className="mr-3 text-2xl text-white">
          <span className="iconify" data-icon="lucide:apple" style={{width: '24px', height: '24px'}}></span>
        </div>
        <div className="text-left">
          <div className="text-xs text-gray-200">تحميل من</div>
          <div className="text-sm font-semibold text-white">App Store</div>
        </div>
      </a>
      
      <a 
        href="https://play.google.com/store/apps/details?id=com.familytree.app" 
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center justify-center rounded-lg bg-black px-5 py-3 transition-all hover:bg-gray-900"
      >
        <div className="mr-3 text-2xl text-white">
          <span className="iconify" data-icon="lucide:play" style={{width: '24px', height: '24px'}}></span>
        </div>
        <div className="text-left">
          <div className="text-xs text-gray-200">تحميل من</div>
          <div className="text-sm font-semibold text-white">Google Play</div>
        </div>
      </a>
    </div>
  );
};

export default MobileAppLinks;
