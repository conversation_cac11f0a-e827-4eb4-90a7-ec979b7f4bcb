import AppModal from '@/components/AppModal';
import PaymentForm from '@/components/PaymentForm';
import { Button } from '@/components/ui/button';
import { discountPackage, packages, type Package } from '@/config/packages';
import { useTenant } from '@/hooks/useUser';
import posthog from 'posthog-js';
import { useState } from 'react';

export function Pricing() {
    const tenant = useTenant();
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);

    const handleBuy = (pkg: Package) => {
        posthog.capture('Pricing: Buy Pressed', {
            nodes: pkg.nodes,
            isDiscounted: pkg === discountPackage,
        });

        setSelectedPackage(pkg);
        setShowPaymentModal(true);
    };

    return (
        <>
            <span className="font-bold">الموقع مجاني حتى ٢٠٠ فرد</span>، وبعدها تعتمد الخدمة على شراء باقات حسب الكميات:
            {/* Special Discount Offer */}
            {tenant?.is_eligible_for_discount && (
                <div className="mt-4">
                    <div className="rounded-lg border-2 border-green-500 bg-green-50 p-6">
                        <div className="mb-4 flex items-center gap-2">
                            <span className="rounded-full bg-green-100 px-3 py-1 text-sm font-bold text-green-700">
                                عرض خاص
                            </span>
                            <span className="text-lg font-bold text-green-700">وفر 15% على باقة الـ ٥٠ فرد! ✨</span>
                        </div>
                        <div className="rounded-lg bg-white p-4 shadow-sm">
                            <div className="flex items-center justify-center gap-4 text-lg">
                                <span className="text-gray-500 line-through opacity-75">
                                    {discountPackage.originalPrice} ريال
                                </span>
                                <span className="font-bold text-green-600">{discountPackage.price} ريال</span>
                            </div>
                            <p className="mt-2 text-center text-gray-600">
                                تقدر تضيف {discountPackage.nodesFormatted} فرد جديد لشجرتك العائلية 🌳
                            </p>
                            <div className="mt-4 text-center">
                                <Button
                                    className="bg-green-600 hover:bg-green-700"
                                    onClick={() => handleBuy(discountPackage)}
                                >
                                    احصل على العرض الخاص
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {/* Regular Packages */}
            <div className="mt-4 grid gap-4 sm:grid-cols-2">
                {packages.map((pkg) => (
                    <div key={pkg.nodes} className="rounded-lg border bg-gray-50 p-4">
                        <h3 className="mb-2 font-semibold">
                            باقة شجرة العائلة - <span className="text-green-600">{pkg.nodesFormatted}</span>
                        </h3>
                        <ul className="text-gray-600">
                            <li>السعر: {pkg.priceFormatted} ريال</li>
                            <li>سعر الفرد: {pkg.pricePerNode} ريال</li>
                            {pkg.savings && <li>التوفير: {pkg.savingsFormatted} ريال</li>}
                        </ul>
                        <Button className="mt-4" size="sm" onClick={() => handleBuy(pkg)}>
                            شراء كمية {pkg.nodesFormatted} فرد
                        </Button>
                    </div>
                ))}
            </div>
            <AppModal open={showPaymentModal} onOpenChange={setShowPaymentModal}>
                {selectedPackage && <PaymentForm pkg={selectedPackage} />}
            </AppModal>
        </>
    );
}

export default Pricing;
