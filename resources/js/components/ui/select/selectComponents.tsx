import { OptionType } from '@/components/ui/select/components';
import { ArrowDownIcon, Cancel01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { ReactNode } from 'react';
import {
    ClearIndicatorProps,
    components,
    DropdownIndicatorProps,
    GroupBase,
    MultiValueRemoveProps,
    SelectComponentsConfig,
} from 'react-select';

export const createSelectComponents = <
    Option = OptionType,
    IsMulti extends boolean = false,
    Group extends GroupBase<Option> = GroupBase<Option>,
>(): SelectComponentsConfig<Option, IsMulti, Group> => ({
    DropdownIndicator: (props: DropdownIndicatorProps<Option, IsMulti, Group>): ReactNode => {
        return (
            <components.DropdownIndicator {...props}>
                <HugeiconsIcon icon={ArrowDownIcon} size={18} />
            </components.DropdownIndicator>
        );
    },
    ClearIndicator: (props: ClearIndicatorProps<Option, IsMulti, Group>): ReactNode => {
        if (props.options.length) {
            return (
                <components.ClearIndicator {...props}>
                    <HugeiconsIcon icon={Cancel01Icon} size={18} />
                </components.ClearIndicator>
            );
        }

        return null;
    },
    MultiValueRemove: (props: MultiValueRemoveProps<Option, IsMulti, Group>): ReactNode => {
        return (
            <components.MultiValueRemove {...props}>
                <HugeiconsIcon icon={Cancel01Icon} size={2} />
            </components.MultiValueRemove>
        );
    },
});
