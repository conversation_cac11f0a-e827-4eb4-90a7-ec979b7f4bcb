import * as DialogPrimitive from '@radix-ui/react-dialog';
import { HugeiconsIcon } from '@hugeicons/react';
import { Cancel01Icon } from '@hugeicons-pro/core-stroke-standard';
import * as React from 'react';

import { cn } from '@/lib/utils';

interface ZoomProps {
  children: React.ReactElement<React.ImgHTMLAttributes<HTMLImageElement>>;
  className?: string;
}

export function Zoom({ children, className }: ZoomProps) {
  const [open, setOpen] = React.useState(false);

  // Clone the child image element to add click handler
  const trigger = React.cloneElement(children, {
    ...children.props,
    className: cn(children.props.className, 'cursor-zoom-in', className),
    onClick: (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation(); // Prevent event bubbling to parent dialog
      setOpen(true);
      // Call original onClick if it exists
      children.props.onClick?.(e);
    },
  });

  return (
    <DialogPrimitive.Root open={open} onOpenChange={setOpen}>
      {trigger}
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay className="fixed inset-0 z-[9999] bg-black/90 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <DialogPrimitive.Content
          className="fixed inset-0 z-[10000] flex items-center justify-center p-4 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
          onOpenAutoFocus={(e) => e.preventDefault()}
          onPointerDownOutside={(e) => {
            e.preventDefault();
            setOpen(false);
          }}
          onEscapeKeyDown={(e) => {
            e.preventDefault();
            e.stopPropagation(); // Prevent escape from closing parent dialog
            setOpen(false);
          }}
        >
          {/* Close button */}
          <DialogPrimitive.Close className="absolute right-4 top-4 z-10 rounded-full bg-black/50 p-2 text-white transition-colors hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black">
            <HugeiconsIcon 
              icon={Cancel01Icon} 
              size={20} 
              strokeWidth={2} 
              className="text-white" 
            />
            <span className="sr-only">إغلاق</span>
          </DialogPrimitive.Close>

          {/* Zoomed image */}
          <div className="relative max-h-full max-w-full">
            <img
              src={children.props.src}
              alt={children.props.alt}
              className="max-h-[90vh] max-w-[90vw] object-contain"
              style={{ 
                cursor: 'zoom-out',
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setOpen(false);
              }}
            />
          </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  );
}
