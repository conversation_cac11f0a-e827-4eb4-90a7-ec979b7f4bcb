import React from 'react';

interface StatsProps {
  nodesCount: number;
  tenantsCount: number;
  usersCount: number;
}

export default function Stats({ nodesCount, tenantsCount, usersCount }: StatsProps) {
  return (
    <div className="relative">
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <dl className="rounded-lg bg-white shadow sm:grid sm:grid-cols-3">
            <div
              className="flex flex-col border-b border-gray-100 p-6 text-center sm:border-0 sm:border-r"
            >
              <dt className="order-2 leading-6 font-bold text-gray-500">عدد الشجرات</dt>
              <dd className="order-1 text-4xl font-extrabold text-green-600">
                <span>
                  {tenantsCount}
                </span>
                <span className="mx-1 text-sm text-gray-400">شجرةً</span>
              </dd>
            </div>
            <div
              className="flex flex-col border-t border-b border-gray-100 p-6 text-center sm:border-0 sm:border-r sm:border-l"
            >
              <dt className="order-2 leading-6 font-bold text-gray-500">عدد الأفراد</dt>
              <dd className="order-1 text-4xl font-extrabold text-green-600">
                <span>{nodesCount}</span>
                <span className="mx-1 text-sm text-gray-400">فردًا</span>
              </dd>
            </div>
            <div
              className="flex flex-col border-t border-gray-100 p-6 text-center sm:border-0 sm:border-l"
            >
              <dt className="order-2 leading-6 font-bold text-gray-500">عدد المستخدمين</dt>
              <dd className="order-1 text-4xl font-extrabold text-green-600">
                <span>{usersCount}</span>
                <span className="mx-1 text-sm text-gray-400">مستخدمًا</span>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
}
