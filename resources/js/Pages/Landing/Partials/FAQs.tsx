import React from 'react';
import ContactUsMobile from '@/components/ContactUsMobile';

export default function FAQs() {
  return (
    <div className="px-12">
      <h1 className="bold w-full pb-2 text-center text-3xl leading-tight">أسئلة شائعة</h1>
      <div className="gradient mx-auto my-0 h-1 w-48 rounded-t py-0 opacity-25" />

      <div>
        <div className="mt-12">
          <dl className="space-y-16 sm:grid sm:grid-cols-2 sm:space-y-0 sm:gap-x-6 sm:gap-y-16 lg:gap-x-10">
            <div>
              <dt className="text-base leading-7 font-semibold text-gray-900">من نحن؟</dt>
              <dd className="mt-2 text-base leading-7 text-gray-600">
                نحن مؤسسة <strong>أوراق</strong> لتقنية المعلومات، نعمل على تطوير خدمات تقنية لخدمة العوائل،
                رقم السجل التجاري: 1010744065
              </dd>
            </div>
            <div>
              <dt className="text-base leading-7 font-semibold text-gray-900">
                هل بيانات عائلتي محمية في الموقع ولا يمكن لأحد الاطلاع عليها؟
              </dt>
              <dd className="mt-2 text-base leading-7 text-gray-600">
                <strong className="text-green-500">نعم بالتأكيد</strong>، نحرص كثيرًا على خصوصية عائلتكم،
                فبياناتكم بعد إدخالها لن تظهر إلا لمسؤولي العائلة، ويمكنكم لاحقًا في الإعدادات السماح بأفراد
                العائلة بالاطلاع على الشجرة.
              </dd>
            </div>
            <div>
              <dt className="text-base leading-7 font-semibold text-gray-900">هل يمكنني تحميل بيانات عائلتي؟</dt>
              <dd className="mt-2 text-base leading-7 text-gray-600">
                نعم يمكنك تحميل جميع بيانات عائلتك في أي وقت بصيغة إكسل.
              </dd>
            </div>
            <div>
              <dt className="text-base leading-7 font-semibold text-gray-900">
                أريد شجرة ورقية فقط، كم سعرها؟ وكيف أطلب تصميمها؟
              </dt>
              <dd className="mt-2 text-base leading-7 text-gray-600">
                سعر الشجرة الورقية يختلف حسب <strong>عدد الأفراد</strong>، يمكنكم طلب شجرة ورقية عن طريق
                التواصل معنا:
                <ContactUsMobile />
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
}
