import { NodesRequestConfig, Tree } from '@/components/tree/Tree';
import { useQueryState } from 'nuqs';
import { ErrorBoundary } from 'react-error-boundary';

const Index = () => {
  const [token] = useQueryState('token');

  const nodesRequestConfig: NodesRequestConfig = {
    url: '/api/v1/mobile/nodes',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  return (
    <ErrorBoundary fallback={<div>sdfsdf</div>} onError={(e, info) => JSON.stringify(info)}>
      <Tree
        nodesRequestConfig={nodesRequestConfig}
        onClickNode={(node) => window.ReactNativeWebView?.postMessage(JSON.stringify({ node_id: node.id }))}
      />
    </ErrorBoundary>
  );
};

export default Index;
