import AppLayout from '@/Layouts/AppLayout';
import FamilyUrl from '@/components/FamilyUrl';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Setting } from '@/types/models';
import { isSlug, trans } from '@/utils/helpers';
import { SquareLock01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { Link, useForm } from '@inertiajs/react';
import { ChangeEvent, useMemo } from 'react';

interface Props {
  settings: Setting[];
  nodeAttributes: string[];
  tenant: {
    has_main_domain: boolean;
  };
}

export default function Edit({ settings, nodeAttributes, tenant }: Props) {
  const getSetting = (key: string) => settings.find((s) => s.key === key)?.value;

  const form = useForm({
    mobile_app_enabled: getSetting('mobile_app_enabled') || false,
    password: getSetting('password') || '',
    allow_guests: getSetting('allow_guests') || false,
    paper_tree_url: null as File | null,
    show_designed_tree: getSetting('show_designed_tree') || false,
    url: getSetting('url') || '',
    default_node_visibility: String(getSetting('default_node_visibility')).split(',') || [],
    hide_females_in_public_page: getSetting('hide_females_in_public_page') || false,
  });

  const paperTreeUrl = getSetting('paper_tree_url');

  const fullUrl = useMemo(() => {
    if (!form.data.url) {
      return '';
    }

    return `https://${form.data.url}.awraq.app`;
  }, [form.data.url]);

  const checkUrl = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      !isSlug(e.key) &&
      e.key !== 'Backspace' &&
      e.key !== 'Delete' &&
      e.key !== 'ArrowLeft' &&
      e.key !== 'ArrowRight'
    ) {
      e.preventDefault();
    }
  };

  const update = () => {
    form.post(route('settings.app.update'), {
      preserveScroll: true,
    });
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      form.setData('paper_tree_url', files[0]);
    }
  };

  const handleCheckboxChange = (attribute: string, checked: boolean) => {
    if (checked) {
      form.setData('default_node_visibility', [...form.data.default_node_visibility, attribute]);
    } else {
      form.setData(
        'default_node_visibility',
        form.data.default_node_visibility.filter((item: string) => item !== attribute),
      );
    }
  };

  return (
    <AppLayout
      header="إعدادات التطبيق"
      topEnd={
        <Button className={form.processing ? 'opacity-25' : ''} disabled={form.processing} onClick={update}>
          حفظ
        </Button>
      }
      footer={
        <Button className={form.processing ? 'opacity-25' : ''} disabled={form.processing} onClick={update}>
          حفظ
        </Button>
      }
    >
      <Box>
        <div className="mb-4">
          <div>
            <div className="flex items-center gap-x-2">
              <Checkbox
                id="mobile_app_enabled"
                checked={!!form.data.mobile_app_enabled}
                onCheckedChange={(checked) => form.setData('mobile_app_enabled', checked as boolean)}
                name="mobile_app_enabled"
              />
              <Label htmlFor="mobile_app_enabled">تفعيل التطبيق</Label>
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-500">يمكن لأفراد العائلة الدخول عن طريق تطبيق شجرة العائلة</p>
          {form.errors.mobile_app_enabled && (
            <p className="mt-2 text-sm text-red-500">{form.errors.mobile_app_enabled}</p>
          )}
        </div>

        {form.data.mobile_app_enabled && (
          <div className={`mb-4 ${form.data.allow_guests ? 'opacity-50 transition' : ''}`}>
            <Input
              value={form.data.password.toString()}
              onChange={(e) => form.setData('password', e.target.value)}
              disabled={!!form.data.allow_guests}
              label="كلمة المرور"
              error={form.errors.password}
              suffix={<HugeiconsIcon icon={SquareLock01Icon} size={20} color="currentColor" strokeWidth={1.5} />}
            />
            <div className="mt-2 text-xs">
              كلمة المرور للدخول للتطبيق. اتركها فارغة لتعطيل تسجيل الدخول بكلمة المرور
            </div>
            {form.data.password ? (
              <div className="mt-2 flex gap-x-1 text-xs font-bold text-green-500">
                <HugeiconsIcon icon={SquareLock01Icon} size={16} color="currentColor" strokeWidth={1.5} />
                <span>يمكن للأفراد الدخول باستخدام كلمة المرور</span>
              </div>
            ) : (
              <div className="mt-2 flex gap-x-1 text-xs font-bold text-red-500">
                <HugeiconsIcon icon={SquareLock01Icon} size={16} color="currentColor" strokeWidth={1.5} />
                <span>لا يمكن للأفراد الدخول باستخدام كلمة المرور، لعدم إدخال كلمة مرور</span>
              </div>
            )}
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div className="mb-4">
            <div className="flex items-center gap-x-2">
              <Checkbox
                id="allow_guests"
                checked={!!form.data.allow_guests}
                onCheckedChange={(checked) => form.setData('allow_guests', checked as boolean)}
                name="allow_guests"
              />
              <Label htmlFor="allow_guests">السماح للزوار بالدخول</Label>
            </div>
            <p className="mt-2 text-xs text-gray-500">يمكن للزوار الدخول للتطبيق بدون تسجيل حساب</p>
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div>
            <hr className="my-6 border-gray-100" />
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div className="mb-4">
            <div className="flex items-center gap-4">
              <Input
                label="الشجرة الورقية"
                type="file"
                accept="application/pdf"
                className="mb-2 flex-1"
                error={form.errors.paper_tree_url}
                onChange={handleFileChange}
                helper="عند رفع شجرة ورقية، فإنها ستظهر لمستخدمي التطبيق"
              />
              {paperTreeUrl && (
                <div className="mb-4">
                  <Button size="sm" variant="link" className="mt-3">
                    <a href={paperTreeUrl.toString()} target="_blank" rel="noopener noreferrer">
                      عرض الشجرة
                    </a>
                  </Button>
                  {getSetting('paper_tree_url') && (
                    <Link
                      href={route('settings.destroy', 'paper_tree_url')}
                      method="delete"
                      className="mt-3 text-red-500"
                    >
                      <Button size="sm" variant="link">
                        حذف الشجرة
                      </Button>
                    </Link>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div>
            <hr className="my-6 border-gray-100" />
          </div>
        )}

        {!tenant.has_main_domain && form.data.mobile_app_enabled && (
          <div>
            <Input
              value={form.data.url.toString()}
              onChange={(e) => form.setData('url', e.target.value)}
              label="رابط صفحة العائلة"
              autoComplete="url"
              error={form.errors.url}
              onKeyDown={checkUrl}
              helper="الرابط يجب أن يكون كلمة أو جملة إنجليزية مع شرطة بدلاً من مسافة. مثال: alkhudiry-family"
            />
            <div className="mt-2">
              <Label>رابط العائلة: </Label>
              <FamilyUrl fullUrl={fullUrl} />
            </div>
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div>
            <hr className="my-6 border-gray-100" />
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div>
            <Label>البيانات الظاهرة في التطبيق</Label>
            <div>
              {nodeAttributes.map((attribute, i) => (
                <label key={i} className="mb-2 flex items-center">
                  <Checkbox
                    checked={form.data.default_node_visibility.includes(attribute)}
                    onCheckedChange={(checked) => handleCheckboxChange(attribute, checked as boolean)}
                  />
                  <span className="mx-1 text-sm">{trans(attribute)}</span>
                </label>
              ))}
              {form.errors.default_node_visibility && (
                <p className="mt-2 text-sm text-red-500">{form.errors.default_node_visibility}</p>
              )}
            </div>
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div>
            <hr className="my-6 border-gray-100" />
          </div>
        )}

        {form.data.mobile_app_enabled && (
          <div>
            <div>
              <label className="mb-2 flex items-center">
                <Checkbox
                  checked={!!form.data.hide_females_in_public_page}
                  onCheckedChange={(checked) => form.setData('hide_females_in_public_page', checked as boolean)}
                />
                <span className="mx-1 text-sm">إخفاء الإناث في التطبيق</span>
              </label>
              {form.errors.hide_females_in_public_page && (
                <p className="mt-2 text-sm text-red-500">{form.errors.hide_females_in_public_page}</p>
              )}
            </div>
          </div>
        )}
      </Box>
    </AppLayout>
  );
}
