import AppGuestLayout from '@/Layouts/AppGuestLayout';
import AuthenticationCard from '@/components/AuthenticationCard';
import ValidationErrors from '@/components/ValidationErrors';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { useForm } from '@inertiajs/react';
import React from 'react';

interface InfoProps {
  identifier?: string;
  identifier_type?: string;
  oauth?: { name: string; email: string };
}

const Info: React.FC<InfoProps> = ({ identifier, identifier_type, oauth }) => {
  const form = useForm<{ name: string; family_name: string; root_name: string }>({
    name: oauth?.name ?? '',
    family_name: '',
    root_name: '',
  });

  const submit = (e: React.FormEvent) => {
    e.preventDefault();
    form.transform((data) => ({
      ...data,
      identifier: oauth ? oauth.email : identifier,
      identifier_type: oauth ? 'email' : identifier_type,
    }));

    form.post(route('register.info.store'));
  };

  return (
    <AppGuestLayout>
      <AuthenticationCard typeName="إنشاء شجرة العائلة">
        <ValidationErrors form={form} />
        <form onSubmit={submit}>
          <div>
            <Input
              value={form.data.name}
              onChange={(e) => form.setData('name', e.target.value)}
              label="اسمك"
              type="text"
              error={form.errors.name}
              required
            />
          </div>

          <hr className="my-8" />

          <div>
            <Input
              value={form.data.family_name}
              onChange={(e) => form.setData('family_name', e.target.value)}
              label="اسم العائلة"
              type="text"
              error={form.errors.family_name}
              required
            />
          </div>

          <div className="mt-4">
            <Input
              value={form.data.root_name}
              onChange={(e) => form.setData('root_name', e.target.value)}
              label="اسم أول فرد في العائلة"
              helper="الفرد الذي تتفرع منه العائلة"
              type="text"
              error={form.errors.root_name}
              required
            />
          </div>

          <Button className="mx-auto mt-4 block w-1/2" type="submit" disabled={form.processing}>
            إنشاء شجرة العائلة
          </Button>
        </form>
      </AuthenticationCard>
    </AppGuestLayout>
  );
};

export default Info;
