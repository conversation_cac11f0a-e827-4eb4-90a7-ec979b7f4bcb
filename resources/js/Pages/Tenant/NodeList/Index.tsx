import AppLayout from '@/Layouts/AppLayout';
import SelectLocation from '@/Pages/Tenant/Branch/Partials/SelectLocation';
import NodeCard from '@/Shared/NodeCard';
import AppModal from '@/components/AppModal';
import Pagination, { PaginationData } from '@/components/Pagination';
import Card from '@/components/ui/Card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RawNode } from '@/lib/designed-tree/components/create-node';
import { useNodeModalStore } from '@/store/node-modal';
import { debounceReload } from '@/utils/helpers';
import { clsx } from 'clsx';
import { useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';

interface Props {
  nodes: PaginationData<RawNode>;
  filters: {
    name?: string;
    gender?: string;
    life_status?: string;
    country_id?: number;
    city_id?: number;
    district_id?: number;
  };
}

export default function Index({ nodes, filters }: Props) {
  const [name, setName] = useQueryState('name');
  const [gender, setGender] = useQueryState('gender');
  const [lifeStatus, setLifeStatus] = useQueryState('life_status');
  const [countryId, setCountryId] = useQueryState('country_id');
  const [cityId, setCityId] = useQueryState('city_id');
  const [districtId, setDistrictId] = useQueryState('district_id');

  const [showNodeModal, setShowNodeModal] = useState(false);
  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  // Initialize form state from filters or query params
  const [location, setLocation] = useState<Location>({
    country: filters.country_id ? { id: filters.country_id, name: '' } : null,
    city: filters.city_id ? { id: filters.city_id, name: '' } : null,
    district: filters.district_id ? { id: filters.district_id, name: '' } : null,
  });

  // Update query parameters when filters change
  useEffect(() => {
    const params = {
      name: name,
      gender: gender,
      life_status: lifeStatus,
      country_id: countryId,
      city_id: cityId,
      district_id: districtId,
    };

    debounceReload(params);
  }, [name, gender, lifeStatus, countryId, cityId, districtId]);

  // Handle location changes
  useEffect(() => {
    if (location.country?.id !== Number(countryId)) {
      setCountryId(location.country?.id?.toString() || null);
    }

    if (location.city?.id !== Number(cityId)) {
      setCityId(location.city?.id?.toString() || null);
    }

    if (location.district?.id !== Number(districtId)) {
      setDistrictId(location.district?.id?.toString() || null);
    }
  }, [cityId, countryId, districtId, location, setCityId, setCountryId, setDistrictId]);

  // Initialize form values from URL params on first load
  useEffect(() => {
    if (name !== undefined && name !== (filters.name || '')) {
      setName(filters.name || null);
    }

    if (gender !== undefined && gender !== (filters.gender || '')) {
      setGender(filters.gender || null);
    }

    if (lifeStatus !== undefined && lifeStatus !== (filters.life_status || '')) {
      setLifeStatus(filters.life_status || null);
    }
  }, []);

  const showNode = (node: RawNode) => {
    setNodeModal('NODE_INFO', node);
    setShowNodeModal(true);
  };

  const resetFilters = () => {
    setName(null);
    setGender(null);
    setLifeStatus(null);
    setCountryId(null);
    setCityId(null);
    setDistrictId(null);
    setLocation({
      country: null,
      city: null,
      district: null,
    });
  };

  return (
    <AppLayout
      header={
        <>
          <div>قائمة الأفراد</div>
          <div className="mt-1 text-xs font-normal text-gray-500">
            هنا يمكنك تصفح جميع أفراد العائلة والبحث عنهم بسهولة
          </div>
        </>
      }
    >
      <div className="flex flex-col gap-4">
        <Card>
          <div className="mb-6 grid gap-4 md:grid-cols-3">
            <div>
              <Label>الاسم</Label>
              <Input
                value={name || ''}
                onChange={(e) => setName(e.target.value || null)}
                placeholder="ابحث بالاسم..."
              />
            </div>
            <div>
              <Label>الجنس</Label>
              <RadioGroup
                value={gender || ''}
                onValueChange={(value) => setGender(value || null)}
                className="mt-2.5 flex"
              >
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="all-gender" value="" />
                  <Label htmlFor="all-gender">الكل</Label>
                </div>
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="male" value="male" />
                  <Label htmlFor="male">الذكور</Label>
                </div>
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="female" value="female" />
                  <Label htmlFor="female">الإناث</Label>
                </div>
              </RadioGroup>
            </div>
            <div>
              <Label>الحالة</Label>
              <RadioGroup
                value={lifeStatus || ''}
                onValueChange={(value) => setLifeStatus(value || null)}
                className="mt-2.5 flex"
              >
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="all-status" value="" />
                  <Label htmlFor="all-status">الكل</Label>
                </div>
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="alive" value="alive" />
                  <Label htmlFor="alive">حي</Label>
                </div>
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="dead" value="dead" />
                  <Label htmlFor="dead">متوفى</Label>
                </div>
                <div className="flex items-center gap-x-1.5">
                  <RadioGroupItem id="unknown" value="unknown" />
                  <Label htmlFor="unknown">غير معروف</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <div className="mb-4">
            <SelectLocation value={location} onChange={setLocation} />
          </div>
          <div className="mb-4 flex justify-between">
            <Button size="sm" variant="outline" onClick={resetFilters}>
              إزالة الفلاتر
            </Button>
          </div>
        </Card>
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الرقم</TableHead>
                <TableHead>الاسم</TableHead>
                <TableHead>الجنس</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>الموقع</TableHead>
                <TableHead>رقم الجوال</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>تاريخ الإضافة</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {nodes.data.length > 0 ? (
                nodes.data.map((node) => (
                  <TableRow key={node.id}>
                    <TableCell>{node.id}</TableCell>
                    <TableCell className="font-semibold">{node.name}</TableCell>
                    <TableCell>
                      <span
                        className={clsx(
                          'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                          {
                            'bg-blue-50 text-blue-700 ring-blue-600/20': node.gender === 'male',
                            'bg-pink-50 text-pink-700 ring-pink-600/20': node.gender === 'female',
                          },
                        )}
                      >
                        {node.gender === 'male' ? 'ذكر' : 'أنثى'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                        className={clsx(
                          'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                          {
                            'bg-green-50 text-green-700 ring-green-600/20': node.life_status === 'alive',
                            'bg-red-50 text-red-700 ring-red-600/20': node.life_status === 'dead',
                            'bg-gray-50 text-gray-700 ring-gray-600/20': node.life_status === 'unknown',
                          },
                        )}
                      >
                        {node.life_status_ar}
                      </span>
                    </TableCell>
                    <TableCell>
                      {node.country || node.city || node.district ? (
                        <div className="text-xs">
                          {[node.country?.name, node.city?.name, node.district?.name].filter(Boolean).join(' - ')}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-400">-</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {node.mobile ? (
                        <div className="text-xs">{node.mobile}</div>
                      ) : (
                        <div className="text-xs text-gray-400">-</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {node.email ? (
                        <div className="text-xs">{node.email}</div>
                      ) : (
                        <div className="text-xs text-gray-400">-</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-xs">{new Date(node.created_at).toLocaleDateString()}</div>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" onClick={() => showNode(node)}>
                        عرض
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    لا يوجد أفراد مطابقة للبحث
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Pagination data={nodes} />
        </Card>
      </div>

      <AppModal open={showNodeModal} onOpenChange={setShowNodeModal}>
        <NodeCard />
      </AppModal>
    </AppLayout>
  );
}
