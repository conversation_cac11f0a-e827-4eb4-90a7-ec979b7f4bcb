import AppLayout from '@/Layouts/AppLayout';
import { fetchNodes } from '@/services/tree.services';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';
import { Sankey<PERSON>hart } from 'echarts/charts';
import { LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { useMemo } from 'react';

// Register necessary ECharts components
echarts.use([Can<PERSON><PERSON><PERSON><PERSON>, SankeyChart, TitleComponent, TooltipComponent, LegendComponent]);

// Configuration constants
const MAX_LEVELS_TO_SHOW = 8;
const MIN_DESCENDANTS = 75;

// Types
interface TreeNode {
  id: number;
  name: string;
  descendants_count: number;
  depth: number;
  children?: TreeNode[];
}

interface SankeyNode {
  name: string;
  label: string;
  value: number;
  depth: number;
}

interface SankeyLink {
  source: string;
  target: string;
  value: number;
}

interface ChartData {
  data: SankeyNode[];
  links: SankeyLink[];
}

// Helper function to truncate text
const truncate = (text: string, length: number): string => {
  if (text.length <= length) return text;
  return text.substring(0, length) + '...';
};

export default function Index() {
  // Flatten tree structure for Sankey chart
  const flattenTree = ({
    node,
    parentNode,
    result,
  }: {
    node: TreeNode | undefined;
    parentNode: TreeNode | null;
    result: ChartData;
  }): void => {
    if (!node || typeof node.id === 'undefined') return;

    // Add node
    result.data.push({
      name: String(node.id),
      label: node.name,
      value: node.descendants_count ?? 0,
      depth: node.depth,
    });

    // Add link
    if (parentNode && typeof parentNode.id !== 'undefined') {
      result.links.push({
        source: String(parentNode.id),
        target: String(node.id),
        value: node.descendants_count ?? 1,
      });
    }

    // Recurse through children
    if (node.children?.length) {
      for (const child of node.children) {
        flattenTree({ node: child, parentNode: node, result });
      }
    }
  };

  // React Query for fetching and processing chart data
  const {
    data: chartData = { data: [], links: [] },
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['distribution-chart-data'],
    queryFn: async (): Promise<ChartData> => {
      // Fetch tree data
      const treeData = await fetchNodes({
        fetchNodeFn: () => axios.get(route('nodes.index'), { responseType: 'stream' }),
      });

      // Flatten the tree
      const flattened: ChartData = { data: [], links: [] };
      flattenTree({ node: treeData, parentNode: null, result: flattened });

      // Normalize depths
      const minDepth = Math.min(...flattened.data.map((item) => item.depth));
      flattened.data.forEach((item) => {
        item.depth = item.depth - minDepth;
      });

      // Filter nodes by depth and minimum descendants
      const filteredNodes = flattened.data.filter(
        (node) => node.depth < MAX_LEVELS_TO_SHOW && node.value >= MIN_DESCENDANTS,
      );

      // Create set of valid node names
      const validNodeNames = new Set(filteredNodes.map((node) => node.name));

      // Filter links to only include valid nodes
      const filteredLinks = flattened.links.filter(
        (link) => validNodeNames.has(link.source) && validNodeNames.has(link.target),
      );

      return {
        data: filteredNodes,
        links: filteredLinks,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const chartDataReady = !isLoading && !isError && chartData.data.length > 0;

  // ECharts configuration
  const option = useMemo(
    () => ({
      textStyle: { fontFamily: 'LamaSans, sans-serif' },
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        position: 'bottom',
        confine: true,
        textStyle: { fontFamily: 'LamaSans, sans-serif' },
        formatter: (params: any) => {
          if (params.dataType === 'node') {
            return `<b>${params.data.label || params.name}</b><br/>عدد الأشخاص: ${params.data.value}`;
          } else if (params.dataType === 'edge') {
            const sourceNode = chartData.data.find((n) => n.name === params.data.source);
            const targetNode = chartData.data.find((n) => n.name === params.data.target);
            const sourceLabel = sourceNode?.label || params.data.source;
            const targetLabel = targetNode?.label || params.data.target;
            return `${sourceLabel} → ${targetLabel}<br/>عدد الأشخاص: ${params.data.value}`;
          }
          return '';
        },
      },
      series: [
        {
          type: 'sankey',
          data: chartData.data,
          links: chartData.links,
          emphasis: { focus: 'adjacency' },
          label: {
            show: true,
            position: 'right',
            backgroundColor: '#f0f0f0dd',
            borderColor: '#AAAAAA',
            borderWidth: 0.5,
            padding: [4, 6],
            borderRadius: 4,
            formatter: ({ data }: { data: any }) => truncate(data.label || data.name || '', 15),
            minMargin: 5,
          },
          itemStyle: { borderWidth: 1, borderColor: '#aaa' },
          lineStyle: { color: 'gradient', curveness: 0.5, opacity: 0.5 },
          edgeLabel: { show: false },
        },
      ],
    }),
    [chartData],
  );

  return (
    <AppLayout>
      <div className="text-lg font-medium">التوزيع العام للشجرة</div>
      <div className="mb-4 text-sm text-gray-500">
        هذه الصفحة مفيدة لمعرفة التقسيم العام للشجرة وأكبر أغصانها (تظهر أول
        {MAX_LEVELS_TO_SHOW} مستويات، وبحد أدنى {MIN_DESCENDANTS} فرد لكل غصن).
      </div>
      <div style={{ height: '70vh', minHeight: '400px', width: '100%' }}>
        {chartDataReady ? (
          <ReactECharts option={option} style={{ height: '100%', width: '100%' }} opts={{ renderer: 'canvas' }} />
        ) : (
          <div className="flex h-full items-center justify-center">جارٍ تحميل البيانات...</div>
        )}
      </div>
    </AppLayout>
  );
}
