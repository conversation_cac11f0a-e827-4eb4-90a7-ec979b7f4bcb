import AppLayout from '@/Layouts/AppLayout';
import NodeCountStats from '@/components/NodeCountStats';
import NodeFilter from '@/components/NodeFilter';
import Title from '@/components/typography/Title';
import { ordinalsAr } from '@/utils/helpers';
import { Link } from '@inertiajs/react';
import React, { useState } from 'react';
import { StatsRow } from './StatsRow';
import { MostCommonNames } from './components/MostCommonNames';
import { Node } from '@/types/models';

export interface StatisticProps {
  aliveNodeNumber: number;
  deadNodeNumber: number;
  aliveMaleNumber: number;
  deadMaleNumber: number;
  aliveFemaleNumber: number;
  deadFemaleNumber: number;
  mostCommonNames: Array<{ name: string; count: number }>;
  mostCommonMaleNames: Array<{ name: string; count: number }>;
  mostCommonFemaleNames: Array<{ name: string; count: number }>;
  mostChildrenNodes: Array<Node & { children_count: number; full_name: string }>;
  mostCities: Array<{ name: string; count: number }>;
  stat: {
    number_of_nodes_in_each_generation: number[];
    most_common_full_names: Array<{ names: Array<{ name: string; count: number }>; length: number }>;
  };
  currentNode?: Node | null;
  initialLabel?: { value: string; label: string } | null;
  initialGender?: string | null;
  initialLocation?: Location | null;
}

export const Index: React.FC<StatisticProps> = ({
  aliveFemaleNumber,
  aliveMaleNumber,
  aliveNodeNumber,
  deadFemaleNumber,
  deadMaleNumber,
  deadNodeNumber,
  mostChildrenNodes,
  mostCities,
  mostCommonFemaleNames,
  mostCommonMaleNames,
  mostCommonNames,
  stat: { most_common_full_names, number_of_nodes_in_each_generation },
}) => {
  const [node, setNode] = useState<Node>();

  return (
    <AppLayout header="الإحصائيات">
      <div>
        <Title text={node ? `إحصائيات ${node.name} وأبناؤه` : 'إحصائيات شجرة العائلة'} />
        <div>
          <NodeFilter resetAfterSelect={true} onSelect={(selectedNode) => setNode(selectedNode)} />
          <div className="my-4" />
        </div>
        <hr className="my-8" />
        <div className="grid gap-12 md:grid-cols-2">
          <NodeCountStats
            aliveNodeNumber={aliveNodeNumber}
            deadNodeNumber={deadNodeNumber}
            aliveMaleNumber={aliveMaleNumber}
            aliveFemaleNumber={aliveFemaleNumber}
            deadMaleNumber={deadMaleNumber}
            deadFemaleNumber={deadFemaleNumber}
          />
          <div>
            <ul className="flex flex-col space-y-4">
              <li className="flex justify-between border-b-2 border-green-100 font-bold">
                <span className="text-gray-800">
                  <span>عدد أفراد كل جيل </span>
                  <span className="text-xs font-normal text-gray-500">الفلاتر لا تنطبق على الإحصائية</span>
                </span>
              </li>
              {number_of_nodes_in_each_generation.map((count, i) => (
                <StatsRow key={`gen-${i}`} label={`الجيل ${ordinalsAr(i + 1)}`} value={count} />
              ))}
            </ul>
          </div>
        </div>
        <div className="mt-12 grid gap-12 md:grid-cols-2 lg:grid-cols-3">
          <div>
            <ul className="flex flex-col space-y-4">
              <li className="flex justify-between border-b-2 border-green-100 font-bold">
                <span className="text-gray-800">أكثر الأسماء تكرارًا</span>
              </li>
              {mostCommonNames.filter(Boolean).map((node, i) => (
                <StatsRow key={`common-${i}`} label={node.name} value={node.count} />
              ))}
            </ul>
          </div>
          <>
            <div>
              <ul className="flex flex-col space-y-4">
                <li className="flex justify-between border-b-2 border-green-100 font-bold">
                  <span className="text-gray-800">أكثر الأسماء تكرارًا (ذكور)</span>
                </li>
                {mostCommonMaleNames.filter(Boolean).map((node, i) => (
                  <StatsRow key={`male-common-${i}`} label={node.name} value={node.count} />
                ))}
              </ul>
            </div>
            <div>
              <ul className="flex flex-col space-y-4">
                <li className="flex justify-between border-b-2 border-green-100 font-bold">
                  <span className="text-gray-800">أكثر الأسماء تكرارًا (إناث)</span>
                </li>
                {mostCommonFemaleNames.filter(Boolean).map((node, i) => (
                  <StatsRow key={`female-common-${i}`} label={node.name} value={node.count} />
                ))}
              </ul>
            </div>
          </>

          <div>
            <ul className="flex flex-col space-y-4">
              <li className="flex justify-between border-b-2 border-green-100 font-bold">
                <span className="text-gray-800">أكثر فرد أبناءً</span>
              </li>
              {mostChildrenNodes
                .filter((n) => n?.children_count)
                .map((node, i) => (
                  <Link
                    key={`children-${i}`}
                    href={route('branches.show', node.id)}
                    className="block hover:bg-green-50"
                  >
                    <StatsRow label={node.full_name} value={node.children_count} />
                  </Link>
                ))}
            </ul>
          </div>
          <div>
            <ul className="flex flex-col space-y-4">
              <li className="flex justify-between border-b-2 border-green-100 font-bold">
                <span className="text-gray-800">أكثر المدن أفرادًا</span>
              </li>
              {mostCities.map((city, i) => (
                <StatsRow key={`city-${i}`} label={city.name} value={city.count} />
              ))}
            </ul>
          </div>
          {most_common_full_names.map((nameGroup, index) => {
            const title =
              {
                3: 'أكثر الأسماء الثلاثية تطابقًا',
                4: 'أكثر الأسماء الرباعية تطابقًا',
                5: 'أكثر الأسماء الخماسية تطابقًا',
                6: 'أكثر الأسماء السداسية تطابقًا',
                default: 'أكثر الأسماء تطابقًا',
              }[nameGroup.length ?? 'default'] ?? '';

            return (
              <div key={`full-name-group-${index}`}>
                <MostCommonNames mostCommonFullNames={nameGroup.names} title={title} />
              </div>
            );
          })}
        </div>
      </div>
    </AppLayout>
  );
};

export default Index;
