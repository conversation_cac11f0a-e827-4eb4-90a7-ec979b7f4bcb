import AppModal from '@/components/AppModal';
import FamilyUrl from '@/components/FamilyUrl';
import { Tree } from '@/components/tree/Tree';
import SubHeader from '@/components/typography/SubHeader';
import { Button } from '@/components/ui/button';
import AppLayout from '@/Layouts/AppLayout';
import { RawNode } from '@/lib/designed-tree/components/create-node';
import GeneralActions from '@/Pages/Tenant/Branch/Partials/GeneralActions';
import { StatsRow } from '@/Pages/Tenant/Statistic/StatsRow';
import { InformationCircleIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { useState } from 'react';

export type TreeSettings = {
  layout_mode: 'auto' | 'manual';
};

type Props = {
  root: RawNode;
  branch: { name: string };
  currentNode: RawNode | null;
  nodeCount: number | string;
  maleNodeCount: number | string;
  femaleNodeCount: number | string;
  settings: TreeSettings;
};

export default function Show({ branch, nodeCount, maleNodeCount, femaleNodeCount }: Props) {
  const [isInfoModalOpen, setInfoModalOpen] = useState(false);

  return (
    <>
      <AppLayout
        withoutPadding
        header={
          <div className="mt-4 px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <SubHeader>{branch.name}</SubHeader>
                <Button variant="outline" size="sm" onClick={() => setInfoModalOpen(true)}>
                  <HugeiconsIcon icon={InformationCircleIcon} size={14} className="text-gray-500" />
                  <span className="text-xs">بيانات الشجرة</span>
                </Button>
              </div>
            </div>
          </div>
        }
      >
        <Tree />
        <div className="flex justify-end p-2">
          <GeneralActions showAddToPaper showUpdateFullName showChangeRoot showExportExcel />
        </div>
      </AppLayout>

      <AppModal open={isInfoModalOpen} onOpenChange={setInfoModalOpen}>
        <div className="p-4">
          <div className="flex items-baseline gap-1">
            <div className="text-sm">
              <span>رابط الشجرة العام: </span>
              <FamilyUrl />
            </div>
          </div>
          <hr className="my-12 border-gray-200" />
          <ul className="mt-4 flex flex-col gap-y-2">
            <StatsRow label="الأفراد" value={nodeCount} />
            <StatsRow label="الذكور" value={maleNodeCount} />
            <StatsRow label="الإناث" value={femaleNodeCount} />
          </ul>
        </div>
      </AppModal>
    </>
  );
}
