import HijriCalendar from '@/components/HijriCalendar';
import { Button } from '@/components/ui/button';
import Card from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import ValidationErrors from '@/components/ValidationErrors';
import { useStreamedNodes } from '@/hooks/use-streamed-nodes';
import SelectLocation from '@/Pages/Tenant/Branch/Partials/SelectLocation';
import { City, Country, District } from '@/services/location.services';
import { Gender, LifeStatus, NodeModel, RelationshipStatus } from '@/types/models';

import { useForm } from '@inertiajs/react';
import { useQueryClient } from '@tanstack/react-query';
import BasicNodeForm from './BasicNodeForm';
import NodeRelationshipsForm from './NodeRelationshipsForm';
import OtherParentNodeForm from './OtherParentNodeForm';

interface Props {
  parent: NodeModel | null;
  selectedNode?: NodeModel;
}

export type InertiaNodeForm = {
  name: string;
  nickname: string;
  order: number;
  bg_color: string;
  size: string;
  label: string;
  location: {
    country: Country | null;
    city: City | null;
    district: District | null;
  };
  gender: Gender;
  life_status: LifeStatus;
  mobile: string;
  email: string;
  birth_date: string;
  death_date: string;
  parent_id: number | null;
  other_parent_relationship_id: number | null;
  about: string;
  relationships: {
    id?: number;
    name?: string;
    family_name?: string;
    status: RelationshipStatus;
    is_outside_family: boolean;
    wife?: { id: number; name: string } | null;
    husband?: { id: number; name: string } | null;
    node?: { id: number; name: string } | null;
    is_new: boolean;
    is_deleted: boolean;
  }[];
  photo: File | null;
};

export default function UpdateNodeForm({ parent, selectedNode }: Props) {
  const queryClient = useQueryClient();
  const { updateNode } = useStreamedNodes();

  const form = useForm<InertiaNodeForm>({
    name: selectedNode?.name ?? '',
    nickname: selectedNode?.nickname ?? '',
    order: selectedNode?.order || 0,
    bg_color: selectedNode?.bg_color ?? '',
    size: selectedNode?.size?.toString() ?? '',
    label: selectedNode?.label ?? '',
    location: {
      country: selectedNode?.country || null,
      city: selectedNode?.city || null,
      district: selectedNode?.district || null,
    },
    gender: selectedNode?.gender ?? 'male',
    life_status: selectedNode?.life_status ?? 'unknown',
    mobile: selectedNode?.mobile ?? '',
    email: selectedNode?.email ?? '',
    birth_date: selectedNode?.birth_date ?? '',
    death_date: selectedNode?.death_date ?? '',
    parent_id: parent?.id || null,
    other_parent_relationship_id: selectedNode?.other_parent_relationship_id || null,
    about: selectedNode?.about ?? '',
    relationships: ((selectedNode?.gender === 'male' ? selectedNode?.wives : selectedNode?.husbands) || []).map(
      (rel) => ({
        id: rel.id,
        name: rel.name,
        family_name: rel.family_name,
        status: rel.status,
        is_outside_family: rel.is_outside_family,
        wife: selectedNode?.gender === 'male' ? (rel.wife ? { id: rel.wife.id, name: rel.wife.name } : null) : null,
        husband:
          selectedNode?.gender === 'male' ? null : rel.husband ? { id: rel.husband.id, name: rel.husband.name } : null,
        node:
          selectedNode?.gender === 'male'
            ? rel.wife
              ? { id: rel.wife.id, name: rel.wife.name }
              : null
            : rel.husband
              ? { id: rel.husband.id, name: rel.husband.name }
              : null,
        is_new: false,
        is_deleted: false,
      }),
    ),
    photo: null,
  });

  form.transform((data) => ({
    ...data,
    birth_date: data.birth_date || null,
    death_date: data.death_date || null,
    country_id: data.location.country?.id,
    city_id: data.location.city?.id,
    district_id: data.location.district?.id,
    relationships: data.relationships.map((rel) => ({
      id: rel.is_new ? null : rel.id,
      node_id: rel.node?.id,
      name: rel.name,
      family_name: rel.family_name,
      status: rel.status,
      is_deleted: rel.is_deleted,
      is_new: rel.is_new,
    })),
  }));

  const submit = () => {
    form.post(route('nodes.update', selectedNode!.id), {
      preserveScroll: true,
      preserveState: true,
      onSuccess: ({ props: { data } }) => {
        const node = (data as { node: NodeModel }).node;

        queryClient.invalidateQueries({ queryKey: ['nodes', node.id] });
        updateNode(node);
      },
    });
  };

  return (
    <div className="grid gap-4 text-right">
      <ValidationErrors form={form} />

      <div className="flex flex-col gap-y-4">
        {parent && <OtherParentNodeForm form={form} parent={parent} />}
        <BasicNodeForm form={form} />
        <NodeRelationshipsForm form={form} />
        <Card title="بيانات إضافية" className="space-y-4">
          <Input
            value={form.data.nickname}
            onChange={(e) => form.setData('nickname', e.target.value)}
            label="اللقب"
            error={form.errors.nickname}
          />
          <Input
            value={form.data.label}
            onChange={(e) => form.setData('label', e.target.value)}
            label="الفرع"
            helper="علامة تدل على منزلة الفرد في الشجرة..."
            error={form.errors.label}
          />
          <Input
            value={form.data.order}
            onChange={(e) => form.setData('order', Number(e.target.value))}
            label="الترتيب"
            type="number"
            helper="ترتيب الفرد بين إخوانه..."
            error={form.errors.order}
          />
          <Label>النبذة</Label>
          <Textarea value={form.data.about} onChange={(e) => form.setData('about', e.target.value)} />
        </Card>

        <Card title="الموقع">
          <SelectLocation value={form.data.location} onChange={(val) => form.setData('location', val)} />
        </Card>

        <Card title="تاريخ الميلاد والوفاة">
          <HijriCalendar
            value={form.data.birth_date}
            onChange={(val) => form.setData('birth_date', val)}
            label="تاريخ الميلاد"
            error={form.errors.birth_date}
          />
          {form.data.life_status === 'dead' && (
            <HijriCalendar
              className="mt-2"
              value={form.data.death_date}
              onChange={(val) => form.setData('death_date', val)}
              label="تاريخ الوفاة"
              error={form.errors.death_date}
            />
          )}
        </Card>
      </div>

      <div className="sticky bottom-0 z-50 flex justify-center rounded-lg border border-gray-200/50 bg-gray-100/25 p-4 shadow-xs backdrop-blur-md">
        <Button onClick={submit} disabled={form.processing} className="w-[70%]">
          تعديل
        </Button>
      </div>
    </div>
  );
}
