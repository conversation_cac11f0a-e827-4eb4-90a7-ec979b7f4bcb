import { AsyncSelect } from '@/components/ui/select/async-select';
import {
  City,
  Country,
  District,
  fetchCities,
  fetchCountries,
  fetchDistricts,
  Location,
} from '@/services/location.services';
import { useEffect, useState } from 'react';

type Props = {
  value: Location;
  onChange: (location: Location) => void;
};

export default function SelectLocation({ value, onChange }: Props) {
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(value.country ?? null);
  const [selectedCity, setSelectedCity] = useState<City | null>(value.city ?? null);
  const [selectedDistrict, setSelectedDistrict] = useState<District | null>(value.district ?? null);

  useEffect(() => {
    if (selectedCountry?.name !== 'السعودية') {
      setSelectedCity(null);
      setSelectedDistrict(null);
    }

    onChange({
      country: selectedCountry,
      city: selectedCity,
      district: selectedDistrict,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCountry, selectedCity, selectedDistrict]);

  return (
    <div className="mt-4 flex flex-wrap gap-4">
      <AsyncSelect<Country>
        label="الدولة"
        value={selectedCountry}
        onChange={setSelectedCountry}
        loadOptions={fetchCountries}
        isClearable
      />
      <AsyncSelect<City>
        label="المدينة"
        value={selectedCity}
        onChange={setSelectedCity}
        loadOptions={fetchCities}
        isClearable
      />
      {selectedCity ? (
        <AsyncSelect<District>
          label="الحي"
          value={selectedDistrict}
          onChange={setSelectedDistrict}
          loadOptions={(search) => fetchDistricts(search, selectedCity.id)}
          isClearable
        />
      ) : null}
    </div>
  );
}
