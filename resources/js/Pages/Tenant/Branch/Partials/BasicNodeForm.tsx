import Card from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { InertiaNodeForm } from '@/Pages/Tenant/Branch/Partials/UpdateNodeForm';
import { Gender, LifeStatus } from '@/types/models';
import { InertiaFormProps } from '@inertiajs/react';

type Props = {
  form: InertiaFormProps<InertiaNodeForm>;
};

export default function BasicNodeForm({ form }: Props) {
  return (
    <Card title="بيانات أساسية">
      <div className="space-y-4">
        <div className="flex gap-x-8">
          <div className="flex-grow space-y-4">
            <Input
              value={form.data.name}
              onChange={(e) => form.setData('name', e.target.value)}
              label="الاسم"
              required
              error={form.errors.name}
            />

            <Input
              value={form.data.mobile}
              onChange={(e) => form.setData('mobile', e.target.value)}
              label="رقم الجوال"
              type="tel"
              pattern="[0-9]*"
              error={form.errors.mobile}
            />
            <Input
              value={form.data.email}
              onChange={(e) => form.setData('email', e.target.value)}
              label="البريد الإلكتروني"
              error={form.errors.email}
            />
          </div>
          <div className="flex flex-col items-center gap-3 self-center">
            <div className="relative">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                {form.data.photo ? (
                  <img
                    src={URL.createObjectURL(form.data.photo)}
                    alt="Preview"
                    className="h-full w-full rounded-full object-cover"
                  />
                ) : (
                  <svg className="h-8 w-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                  </svg>
                )}
              </div>
            </div>
            <label className="cursor-pointer rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-semibold hover:bg-gray-50">
              تغيير الصورة
              <input
                type="file"
                accept="image/*"
                className="sr-only"
                onChange={(e) => form.setData('photo', e.target.files?.[0] ?? null)}
              />
            </label>
          </div>
        </div>
        <div className="flex justify-between gap-x-2">
          <div>
            <Label>الجنس</Label>
            <RadioGroup
              value={form.data.gender}
              onValueChange={(val) => form.setData('gender', val as Gender)}
              className="mt-2 flex"
            >
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="male" value="male" />
                <Label htmlFor="male">ذكر</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="female" value="female" />
                <Label htmlFor="female">أنثى</Label>
              </div>
            </RadioGroup>
          </div>
          <div>
            <Label>الحالة</Label>
            <RadioGroup
              value={form.data.life_status}
              onValueChange={(val) => form.setData('life_status', val as LifeStatus)}
              className="mt-2 flex"
            >
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="alive" value="alive" />
                <Label htmlFor="alive">حى</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="dead" value="dead" />
                <Label htmlFor="deceased">متوفى</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="unknown" value="unknown" />
                <Label htmlFor="unknown">غير معروف</Label>
              </div>
            </RadioGroup>
          </div>
        </div>
      </div>
    </Card>
  );
}
