import AppLayout from '@/Layouts/AppLayout';
import Helper from '@/components/typography/Helper';
import Card from '@/components/ui/Card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useChangedFields } from '@/hooks/useChangedFields';
import { RawNode } from '@/lib/designed-tree/components/create-node';
import { navigateToNode, truncate } from '@/utils/helpers';
import { router } from '@inertiajs/react';
import { useQueryState } from 'nuqs';

interface NodeChange {
  id: number;
  node: { id: number; name: string };
  member?: { node?: { full_name: string } };
  status: string;
  status_ar: string;
  old_attributes?: RawNode;
  new_attributes?: RawNode;
  created_at: string;
}

interface NodeChangePageProps {
  nodeChanges: { data: NodeChange[] };
}

function isColor(val: unknown) {
  return typeof val === 'string' && val.startsWith('#') && val.length === 7;
}

function getClass(status: string) {
  if (status === 'pending') return 'text-yellow-500';
  if (status === 'approved') return 'text-green-500';
  if (status === 'rejected') return 'text-red-500';
  return '';
}

export default function NodeChangeIndex({ nodeChanges }: NodeChangePageProps) {
  const [status, setStatus] = useQueryState('status');
  const { getChangedFields } = useChangedFields();

  const filteredChanges = status ? nodeChanges.data.filter((c) => c.status === status) : nodeChanges.data;

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus || null);
    router.visit(route('node-changes.index', newStatus ? { status: newStatus } : {}), { preserveState: true });
  };

  return (
    <AppLayout
      header={
        <div>
          طلبات التعديل في الشجرة
          <Helper className="mt-1 font-normal">هنا ستظهر طلبات التعديل من قبل أفراد العائلة من خلال التطبيق</Helper>
        </div>
      }
      topEnd={
        <a href={route('node-changes.export')}>
          <Button size="sm">Excel</Button>
        </a>
      }
    >
      <Card>
        <div className="mb-4 flex justify-between">
          <div>
            <Label>تصفية حسب الحالة</Label>
            <RadioGroup value={status || ''} onValueChange={handleStatusChange} className="mt-2.5 flex">
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="pending" value="pending" />
                <Label htmlFor="pending">تحت الانتظار</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="approved" value="approved" />
                <Label htmlFor="approved">موافق</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="rejected" value="rejected" />
                <Label htmlFor="rejected">مرفوض</Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الفرد</TableHead>
              <TableHead>مقدم الطلب</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>التعديلات</TableHead>
              <TableHead>تاريخ الطلب</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredChanges.map((change) => (
              <TableRow key={change.id}>
                <TableCell className="font-semibold">{change.node?.name}</TableCell>
                <TableCell>{truncate(change.member?.node?.full_name ?? '', 100)}</TableCell>
                <TableCell className="flex items-center">
                  <div className={`text-xs font-medium text-gray-900 ${getClass(change.status)}`}>
                    {change.status_ar}
                  </div>
                </TableCell>
                <TableCell>
                  {change.old_attributes
                    ? getChangedFields({
                        oldValues: change.old_attributes ?? change.node,
                        newValues: change.new_attributes,
                        event: 'updated',
                      }).map((c, idx) =>
                        c ? (
                          <div key={idx} className="mb-1">
                            <div className="font-bold">{c.label}:</div>
                            <div className="flex gap-1">
                              {isColor(c.oldValue) ? (
                                <span className="h-4 w-4" style={{ backgroundColor: c.oldValue }} />
                              ) : (
                                <span className="text-red-500">{c.oldValue}</span>
                              )}
                              <span>←</span>
                              {isColor(c.newValue) ? (
                                <span className="h-4 w-4" style={{ backgroundColor: c.newValue }} />
                              ) : (
                                <span className="text-green-500">{c.newValue}</span>
                              )}
                            </div>
                          </div>
                        ) : (
                          <div className="rounded bg-gray-100 p-4 text-center font-bold text-gray-400">
                            تم الموافقة على التعديلات
                          </div>
                        ),
                      )
                    : null}
                </TableCell>
                <TableCell>{new Date(change.created_at).toLocaleDateString()}</TableCell>
                <TableCell className="flex gap-2">
                  <Button size="sm" variant="secondary" onClick={() => navigateToNode(change.node.id)}>
                    عرض
                  </Button>
                  {change.status === 'pending' && (
                    <>
                      <Button size="sm" onClick={() => router.post(route('node-changes.approve', change.id))}>
                        موافقة
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => router.post(route('node-changes.reject', change.id))}
                      >
                        رفض
                      </Button>
                    </>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </AppLayout>
  );
}
