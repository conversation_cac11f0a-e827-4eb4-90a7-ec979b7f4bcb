'use client';

import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { useUser } from '@/hooks/useUser';
import { useMemo } from 'react';

interface ErrorProps {
  status: string;
}

export function Error({ status }: ErrorProps) {
  const user = useUser();

  const title = useMemo(() => {
    return {
      '503': 'الموقع تحت الصيانة حاليا',
      '500': 'ثمة خلل في الموقع',
      '404': 'الصفحة غير موجودة',
      '403': 'لا يسمح لك بدخول هذه الصفحة',
      '401': 'لا يسمح لك بدخول هذه الصفحة',
    }[status];
  }, [status]);

  const description = useMemo(() => {
    return {
      '503': 'نعمل على تحديث الموقع. انتظر قليلًا',
      '500': 'ثمة خلل في نفس الموقع. سنقوم بإصلاحه بأقرب وقت.',
      '404': 'الصفحة التي تريدها ليست موجودة.',
      '403': 'لا تملك الصلاحيات اللازمة لدخول هذه الصفحة.',
      '401': 'لا تملك الصلاحيات اللازمة لدخول هذه الصفحة.',
    }[status];
  }, [status]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50">
      <h1 className="text-7xl font-bold text-green-500">
        {status}
      </h1>
      <h1 className="text-3xl">
        {title}
      </h1>
      <hr className="my-8 h-1 w-3/5" />
      <div className="mb-8 text-xl">
        {description}
      </div>
      <Link href={user ? route('branches.show') : route('welcome')}>
        <Button>الصفحة الرئيسية</Button>
      </Link>
    </div>
  );
}