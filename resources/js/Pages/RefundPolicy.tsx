import React from 'react';
import AppGuestLayout from '@/Layouts/AppGuestLayout';
import Logo from '@/components/Logo';

export default function RefundPolicy() {
  return (
    <AppGuestLayout>
      <div className="flex min-h-screen flex-col text-sm">
        {/* Header */}
        <header className="mt-12 flex justify-center">
          <Logo width={100} className="text-green-600" />
        </header>

        {/* Main Content */}
        <main className="container mx-auto grow px-4">
          <div className="mt-8 rounded-lg border bg-white p-6">
            <h1 className="mb-6 text-xl font-bold">سياسة الاسترجاع والاستبدال</h1>
            <div className="space-y-6 text-gray-700">
              <section>
                <h2 className="mb-3 font-bold">١. شروط عامة:</h2>
                <ul className="list-inside list-disc space-y-2 pr-4">
                  <li>تعتبر الخدمة مفعّلة بمجرد إضافة الأفراد الإضافيين إلى حساب المستخدم</li>
                  <li>يحق للعميل طلب استرجاع المبلغ خلال ٧ أيام من تاريخ الشراء</li>
                  <li>
                    في حال تم استخدام الخدمة بإضافة أكثر من ٥٠٪ من عدد الأفراد المشمولين في الباقة، لا
                    يمكن استرجاع المبلغ
                  </li>
                </ul>
              </section>

              <section>
                <h2 className="mb-3 font-bold">٢. حالات الاسترجاع:</h2>
                <ul className="list-inside list-disc space-y-2 pr-4">
                  <li>خلال فترة ٧ أيام من الشراء</li>
                  <li>وجود خلل تقني يمنع الاستفادة من الخدمة بشكل كامل</li>
                  <li>الدفع المتكرر عن طريق الخطأ لنفس الباقة</li>
                </ul>
              </section>

              <section>
                <h2 className="mb-3 font-bold">٣. آلية الاسترجاع:</h2>
                <ul className="list-inside list-disc space-y-2 pr-4">
                  <li>
                    يتم تقديم طلب الاسترجاع على{' '}
                    <a
                      href="https://wa.me/966550438831"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      واتساب 966550438831
                    </a>
                  </li>
                  <li>يتم مراجعة الطلب والرد خلال ٢٤ ساعة عمل</li>
                  <li>في حال الموافقة، يتم إرجاع المبلغ بنفس طريقة الدفع الأصلية</li>
                </ul>
              </section>

              <section>
                <h2 className="mb-3 font-bold">٤. ملاحظات مهمة:</h2>
                <ul className="list-inside list-disc space-y-2 pr-4">
                  <li>الخدمة غير قابلة للتحويل لحساب مستخدم آخر</li>
                  <li>نحتفظ بحق رفض طلبات الاسترجاع المخالفة للشروط المذكورة</li>
                </ul>
              </section>

              <section>
                <h2 className="mb-3 font-bold">٥. التواصل:</h2>
                <p className="pr-4">
                  للاستفسارات حول سياسة الاسترجاع أو تقديم طلب استرجاع، يرجى التواصل معنا عبر:
                </p>
                <p className="mt-2 pr-4">
                  <a
                    href="https://wa.me/966550438831"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    واتساب 966550438831
                  </a>
                </p>
              </section>
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="py-4 text-center text-sm text-gray-500">
          &copy; {new Date().getFullYear()} شجرة العائلة. جميع الحقوق محفوظة.
        </footer>
      </div>
    </AppGuestLayout>
  );
}
