import AppModal from '@/components/AppModal';
import { AppSidebar } from '@/components/AppSidebar';
import MaxNodesDiscountModal from '@/components/MaxNodesDiscountModal';
import { Button } from '@/components/ui/button';
import { SidebarProvider, SidebarTrigger, useSidebar } from '@/components/ui/sidebar';
import { useTenant, useUser } from '@/hooks/useUser';
import { NodeModalType, useNodeModalStore } from '@/store/node-modal';
import { usePaymentModalStore } from '@/store/payment-modal';
import { Link, usePage } from '@inertiajs/react';
import posthog from 'posthog-js';
import { PropsWithChildren, ReactNode, useEffect } from 'react';
import { toast } from 'sonner';

type AppLayoutProps = {
  unstyled?: boolean;
  header?: ReactNode;
  content?: ReactNode;
  topEnd?: ReactNode;
  footer?: ReactNode;
  withoutPadding?: boolean;
};

export default function AppLayout({
  unstyled = false,
  header,
  content,
  children,
  topEnd,
  footer,
  withoutPadding = false,
}: PropsWithChildren<AppLayoutProps>) {
  return (
    <SidebarProvider>
      <AppLayoutContent
        unstyled={unstyled}
        header={header}
        content={content}
        topEnd={topEnd}
        footer={footer}
        withoutPadding={withoutPadding}
      >
        {children}
      </AppLayoutContent>
    </SidebarProvider>
  );
}

const AppLayoutContent = ({
  unstyled = false,
  header,
  content,
  children,
  topEnd,
  footer,
  withoutPadding,
}: PropsWithChildren<AppLayoutProps>) => {
  const user = useUser()!;
  const page = usePage();
  const isMaxNodesDiscountModalShown = useNodeModalStore((s) => s.activeModal === NodeModalType.MAX_NODES_DISCOUNT);
  const sidebar = useSidebar();

  // Show toast on first error
  useEffect(() => {
    const firstError = Object.values(page.props.errors ?? {})[0];
    if (firstError) {
      toast.error(firstError as string);
    }
  }, [page.props.errors]);

  // Identify user in PostHog
  useEffect(() => {
    if (user?.id) {
      posthog.identify(user.id.toString(), {
        name: user.name,
        email: user.email,
        mobile: user.mobile,
      });
    }
    // eslint-disable-next-line
  }, [user?.id]);

  return (
    <>
      <AppSidebar />
      {sidebar.isMobile && (
        <Button
          variant="outline"
          size="icon"
          className="fixed top-2.5 right-2.5 z-50 size-13 rounded-full bg-white shadow-lg"
          onClick={(e) => {
            sidebar.toggleSidebar();
            e.stopPropagation();
          }}
        >
          <div className="flex flex-col items-center gap-y-0.5">
            <SidebarTrigger className="pointer-events-none" size={15} />
            <span className="text-[7px] font-bold text-gray-600">فتح القائمة</span>
          </div>
        </Button>
      )}
      <main className="bg-sidebar w-full text-gray-800">
        <DemoBanner />
        <MaxNodesBanner />
        {header || !sidebar.isMobile ? null : null}
        <div className={!withoutPadding ? 'px-6 py-4' : ''}>
          <div className="flex items-center">
            {!unstyled && (
              <div className="flex w-full flex-wrap items-center justify-between sm:flex-nowrap">
                {header ? (
                  <div className="flex items-center gap-5">
                    <header>
                      <span className="leading-tight font-medium">{header}</span>
                    </header>
                  </div>
                ) : (
                  <div></div>
                )}
                <div>{topEnd}</div>
              </div>
            )}
          </div>

          {unstyled ? (
            <div className="relative">{content ?? children}</div>
          ) : (
            (content ?? children) && (
              <main className={`${header ? 'mt-4' : ''}`}>
                {content ?? children}
                <div className="mt-4">{footer}</div>
              </main>
            )
          )}
        </div>
      </main>
      <AppModal open={isMaxNodesDiscountModalShown}>
        <MaxNodesDiscountModal />
      </AppModal>
    </>
  );
};

const MaxNodesBanner = () => {
  const tenant = useTenant()!;
  const user = useUser()!;

  const setNodeModal = useNodeModalStore((state) => state.setNodeModal);
  const openPricingModal = usePaymentModalStore((state) => state.openPricingModal);

  if (user.is_demo) {
    return null;
  }

  if (!tenant.did_reach_max_number_of_nodes) {
    return null;
  }

  return (
    <div className={`border-b p-3 text-white ${tenant.is_eligible_for_discount ? 'bg-green-600' : 'bg-red-500'}`}>
      <div className="flex items-center justify-between md:container md:mx-auto">
        <div>
          <p className="mb-1 text-sm font-medium">
            لقد وصلتم للحد الأقصى من الأفراد <strong>({tenant.max_nodes_number} فرد)</strong>.
          </p>
          <div className="text-sm">
            {tenant.is_eligible_for_discount ? (
              <>
                <strong>عرض خاص:</strong> احصل على 50 فرد إضافي بخصم 15%!{' '}
                <button className="font-bold underline" onClick={() => setNodeModal(NodeModalType.MAX_NODES_DISCOUNT)}>
                  اضغط هنا
                </button>
              </>
            ) : (
              <>
                لشراء كميات إضافية من الأفراد{' '}
                <button className="underline" onClick={() => openPricingModal()}>
                  اضغط هنا
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const DemoBanner = () => {
  const user = useUser()!;

  if (!user.is_demo) {
    return null;
  }

  return (
    <div className="border-b bg-green-500 p-3 text-white">
      <div className="flex items-center justify-between md:container md:mx-auto">
        <div>
          <p className="mb-1 text-sm font-medium">أنت الأن داخل لحساب النسخة التجريبية</p>
          <div className="text-xs">
            <span>أعجبتك المنصة؟ سجل حسابك من هذا الرابط </span>
            <span>
              <Link href={route('logout')} method="post" as="button" className="ml-1 underline">
                awraq.app
              </Link>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
