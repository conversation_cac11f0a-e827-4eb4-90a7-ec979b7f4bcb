import { usePage } from '@inertiajs/react';

export type Branch = {
    id: number;
    name: string;
    tenant_id: number;
};

export type Tenant = {
    id: number;
    created_at: string;
    max_nodes_number: number;
    all_nodes_count: number;
    tree_url: string;
    did_reach_max_number_of_nodes: boolean;
    is_eligible_for_discount: boolean;
    branches: Branch[];
    owner?: User;
};

export type User = {
    id: number;
    name: string;
    mobile: string;
    email?: string;
    tenant_id: number;
    is_owner: boolean;
    resource_name: string;
    is_demo: boolean;
    tenant?: Tenant;
};

export type Admin = {
    id: number;
    name: string;
    email: string;
};

export const useUser = () => usePage().props.user as User | undefined;

export const useAdmin = () => usePage().props.admin as Admin | undefined;

export const useTenant = () => {
    const pageTenant = usePage().props?.tenant;
    const userTenant = useUser()?.tenant;

    return (pageTenant ?? userTenant) as Tenant | undefined;
};

export default useUser;
