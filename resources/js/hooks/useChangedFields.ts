import { RawNode } from '@/lib/designed-tree/components/create-node';

type ChangedField = {
  key: string;
  label: string;
  oldValue: string;
  newValue: string;
};

interface GetChangedFieldsArgs {
  oldValues: RawNode;
  newValues: RawNode;
  event: string;
}

export const useChangedFields = () => {
  const translations: Record<string, string> = {
    name: 'الاسم',
    life_status: 'الحالة',
    birth_date: 'تاريخ الميلاد',
    death_date: 'تاريخ الوفاة',
    mobile: 'رقم الجوال',
    email: 'البريد الإلكتروني',
    gender: 'الجنس',
    parent_id: 'الأب',
    about: 'النبذة',
    nickname: 'اللقب',
    country_id: 'الدولة',
    city_id: 'المدينة',
    district_id: 'الحي',
    label: 'الفرع',
    x: 'الموقع في الخريطة',
    y: 'الموقع في الخريطة',
    bg_color: 'لون الخلفية',
    size: 'الحجم',
    order: 'الترتيب',
    photo: 'الصورة الشخصية',
    added_to_paper_at: 'الإضافة إلى الشجرة الورقية',
  };

  const keysToIgnore = [
    'id',
    'updated_at',
    'created_at',
    'full_name',
    'is_root',
    'country_id',
    'city_id',
    'district_id',
    'style',
  ];

  const oldValuesTranslations: Record<string, string> = {
    photo: 'صورة قديمة',
    added_to_paper_at: 'لا',
    male: 'ذكر',
    female: 'أنثى',
    alive: 'حي',
    dead: 'متوفى',
    unknown: 'غير معروف',
  };

  const newValuesTranslations: Record<string, string> = {
    photo: 'صورة جديدة',
    added_to_paper_at: 'نعم',
    male: 'ذكر',
    female: 'أنثى',
    alive: 'حي',
    dead: 'متوفى',
    unknown: 'غير معروف',
  };

  function getChangedFields({ oldValues, newValues, event }: GetChangedFieldsArgs): ChangedField[] {
    if (event !== 'updated') return [];
    if (!oldValues || !newValues) return [];
    const changed: ChangedField[] = [];
    for (const key in newValues) {
      if (keysToIgnore.includes(key)) continue;
      const oldVal = oldValues[key];
      const newVal = newValues[key];
      if (oldVal !== newVal) {
        changed.push({
          key,
          label: translations[key] || key,
          oldValue: oldValuesTranslations[oldVal] || String(oldVal ?? ''),
          newValue: newValuesTranslations[newVal] || String(newVal ?? ''),
        });
      }
    }
    return changed;
  }

  return { getChangedFields };
};
