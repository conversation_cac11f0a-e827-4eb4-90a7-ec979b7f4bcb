import { create } from 'zustand';
import { Node } from '@/types/models';

export const NodeModalType = {
  NONE: 'NONE',
  NODE_INFO: 'NODE_INFO',
  NODE_ADDITION: 'NODE_ADDITION',
  NODE_CHANGE: 'NODE_CHANGE',
  MAX_NODES_DISCOUNT: 'MAX_NODES_DISCOUNT',
} as const;

export type NodeModalTypeValue = (typeof NodeModalType)[keyof typeof NodeModalType];

type NodeModalState = {
  activeModal: NodeModalTypeValue;
  selectedNode: Node | null;
};

type NodeModalActions = {
  setNodeModal: (modalType: NodeModalTypeValue, node?: Node | null) => void;
  hideNodeModal: () => void;
  setSelectedNode: (node: Node | null) => void;
};

type NodeModalStore = NodeModalState & NodeModalActions;

export const useNodeModalStore = create<NodeModalStore>((set) => ({
  activeModal: NodeModalType.NONE,
  selectedNode: null,

  setNodeModal: (modalType, node = null) =>
    set({
      activeModal: modalType,
      selectedNode: node,
    }),

  hideNodeModal: () =>
    set({
      activeModal: NodeModalType.NONE,
      selectedNode: null,
    }),

  setSelectedNode: (node) => set({ selectedNode: node }),
}));
