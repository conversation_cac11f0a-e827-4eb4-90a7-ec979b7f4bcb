<?php

use App\Http\Controllers\Auth\OTPController;
use App\Http\Controllers\Auth\User\Login\DemoLoginController;
use App\Http\Controllers\Auth\User\Login\GoogleLoginController;
use App\Http\Controllers\Auth\User\Login\LoginAfterOTPController;
use App\Http\Controllers\Auth\User\Login\LoginController;
use App\Http\Controllers\Auth\User\LogoutController;
use App\Http\Controllers\Auth\User\Register\RegisterInfoController;

Route::middleware(['guest', 'throttle:login'])
    ->prefix('auth')
    ->group(function () {
        Route::prefix('login')
            ->name('login.')
            ->group(function () {
                Route::get('', [LoginController::class, 'create'])->name('create');
                Route::post('', [LoginController::class, 'store'])->name('store');

                Route::post('demo', DemoLoginController::class)->name('login-demo');

                Route::get('login-after-otp', LoginAfterOTPController::class)->name(
                    'login-after-otp'
                );

                // Google login routes
                Route::get('google', [GoogleLoginController::class, 'redirect'])->name('google');
                Route::get('google/callback', [GoogleLoginController::class, 'callback'])->name('google.callback');
            });

        Route::prefix('register')
            ->name('register.')
            ->group(function () {
                Route::prefix('info')
                    ->name('info.')
                    ->group(function () {
                        Route::get('', [RegisterInfoController::class, 'create'])->name('create');
                        Route::post('', [RegisterInfoController::class, 'store'])->name('store');
                    });
            });

        Route::get('otp', [OTPController::class, 'create'])->name('otp.create');
        Route::post('otp', [OTPController::class, 'store'])->name('otp.store');
    });

Route::post('/auth/logout', LogoutController::class)->name('logout');
