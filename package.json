{"name": "awraq-mobile-app", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "build:ios:development": "eas build --profile development --platform ios --local", "build:android:development": "eas build --profile development --platform android --local", "build:all:development": "eas build --profile development --local", "--------------------------": "--------------------------", "build:ios:preview": "eas build --profile preview --platform ios", "build:android:preview": "eas build --profile preview --platform android", "build:all:preview": "eas build --profile preview", "------------------------": "--------------------------", "build:ios:production": "eas build --profile production --platform ios --auto-submit", "build:android:production": "eas build --profile production --platform android --auto-submit", "build:all:production": "eas build --profile production --platform all --auto-submit --non-interactive --no-wait", "-------------------------": "-------------------------", "submit:ios:production": "eas submit --platform ios --latest", "submit:android:production": "eas submit --platform android --latest"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.3", "@hookform/resolvers": "^4.0.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@sentry/react-native": "~6.3.0", "@shopify/react-native-skia": "1.5.0", "@tanstack/query-sync-storage-persister": "^5.51.16", "@tanstack/react-query": "^5.51.16", "@tanstack/react-query-persist-client": "^5.51.16", "axios": "^1.7.2", "dayjs": "^1.11.12", "expo": "^52.0.0", "expo-application": "~6.0.2", "expo-build-properties": "~0.13.2", "expo-calendar": "~14.0.6", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.19", "expo-device": "~7.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-image": "~2.0.7", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-notifications": "~0.29.14", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "hijri-js": "^1.0.25", "posthog-react-native": "^3.13.0", "posthog-react-native-session-replay": "^1.0.5", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-native": "0.76.9", "react-native-actions-sheet": "^0.9.7", "react-native-animated-spinkit": "^1.5.2", "react-native-blob-util": "^0.19.11", "react-native-confirmation-code-field": "^7.4.0", "react-native-device-info": "^11.1.0", "react-native-gesture-handler": "~2.20.2", "react-native-image-modal": "^3.0.13", "react-native-mmkv": "^2.12.2", "react-native-notifier": "^2.0.0", "react-native-pdf": "^6.7.7", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "victory-native": "^41.16.1", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.0.7", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.2.1", "jest-expo": "~52.0.6", "prettier": "^3.3.3", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}