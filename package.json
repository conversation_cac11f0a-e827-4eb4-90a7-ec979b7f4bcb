{"name": "awn", "version": "0.0.1", "description": "عون (Awn) - A shadcn-style utility library for TypeScript. Copy utilities into your project.", "type": "module", "publishConfig": {"access": "public"}, "bin": {"awn": "./dist/cli.js"}, "files": ["dist", "registry"], "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest", "test:run": "vitest run", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\" \"registry/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"registry/**/*.ts\"", "typecheck": "tsc --noEmit", "semantic-release": "semantic-release", "docs:generate": "cd docs/src && node generate-docs.js", "docs:serve": "cd docs/public && npx serve -p 8000"}, "dependencies": {"chalk": "^5.3.0", "commander": "^12.1.0", "fs-extra": "^11.2.0", "fuse.js": "^7.0.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "semantic-release": "^24.2.5", "typescript": "~5.8.3", "vitest": "^1.0.0"}, "keywords": ["utilities", "typescript", "lodash", "shadcn", "cli"], "author": "<PERSON><PERSON><PERSON> <https://github.com/Muhammed<PERSON>y>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/MuhammedAlkhudiry/awn.git"}, "homepage": "https://github.com/MuhammedAlkhudiry/awn#readme", "bugs": {"url": "https://github.com/MuhammedAlkhudiry/awn/issues"}}