import { sentryVitePlugin } from '@sentry/vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'node:path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ command }) => {
  const env = loadEnv(command, process.cwd());

  return {
    build: {
      sourcemap: true,
    },
    plugins: [
      laravel({
        input: ['resources/css/app.css', 'resources/js/app.tsx'],
        ssr: 'resources/js/ssr.tsx',
        refresh: true,
      }),
      react(),
      tailwindcss(),
      sentryVitePlugin({
        org: 'muhammed-alkhudiry',
        project: 'awraq',
        authToken: env.VITE_SENTRY_AUTH_TOKEN,
      }),
    ],
    esbuild: {
      jsx: 'automatic',
    },
    resolve: {
      alias: {
        'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
      },
    },
  };
});
